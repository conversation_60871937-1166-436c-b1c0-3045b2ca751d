---
description: 
globs: 
alwaysApply: true
---
# 管理后台开发规范

本规则适用于企业官网管理后台的功能设计、权限控制、内容管理与SEO优化。

## 1. 功能模块
- 公司信息管理：公司简介、联系方式、LOGO等
- 企业文化、新闻、文章管理
- 产品信息管理：产品内容、图片、分类、标签等
- 用户访问数据统计与分析
- SEO优化设置：页面title、meta、sitemap等
- 管理员账号与权限管理
- 文章/产品发布、编辑、下架、置顶等

## 2. 权限与安全
- 支持多角色权限（如超级管理员、内容编辑、运营等）
- 重要操作需日志记录，支持操作审计
- 管理后台需登录认证，支持Token/JWT等安全机制
- 敏感信息加密存储，防止泄露

## 3. UI与交互
- 采用现代化UI框架（如Element Plus、Ant Design Vue等）
- 支持响应式布局，兼容主流浏览器
- 操作流程简洁，表单校验友好，错误提示明确

## 4. 其他建议
- 支持批量导入/导出、数据备份与恢复
- 支持多语言（如有需求）
- 后台操作应有权限校验与安全防护

## 相关文件引用
- [项目结构说明](mdc:project-structure.mdc)
- [SEO优化规则](mdc:seo-guidelines.mdc)
- [企业信息与内容管理规则](mdc:company-content.mdc)
- [产品展示与管理规则](mdc:product-management.mdc)
- [用户访问与数据采集规则](mdc:user-visit.mdc)

