---
description: 
globs: 
alwaysApply: true
---
# 项目结构说明

本项目为企业POS产品展示与宣传网站，采用 Node.js + Vue.js + TypeScript + TailwindCSS 技术栈，支持现代化UI、SEO优化、内容管理和管理后台。

## 推荐目录结构

```
/                  # 项目根目录
├── backend/       # Node.js 后端服务（API、内容管理、用户访问记录等）
│   ├── src/       # 后端源码
│   ├── config/    # 配置文件
│   └── ...
├── frontend/      # Vue.js 前端项目（企业官网、产品展示、SEO优化等）
│   ├── src/       # 前端源码
│   ├── public/    # 静态资源
│   ├── tailwind.config.js # TailwindCSS 配置
│   └── ...
├── admin/         # 管理后台前端（独立Vue项目，管理公司、产品、文章、SEO等）
│   ├── src/       # 管理后台源码
│   └── ...
├── shared/        # 通用类型、工具、接口定义等
├── package.json   # 项目依赖管理
├── README.md      # 项目说明
└── ...
```

## 相关规则文件引用
- [SEO优化规则](mdc:seo-guidelines.mdc)
- [企业信息与内容管理规则](mdc:company-content.mdc)
- [产品展示与管理规则](mdc:product-management.mdc)
- [用户访问与数据采集规则](mdc:user-visit.mdc)
- [管理后台开发规范](mdc:admin-panel.mdc)
- [UI与交互设计规范](mdc:ui-ux-guidelines.mdc)
- [技术栈与依赖管理](mdc:tech-stack.mdc)

## 说明
- 前后端、管理后台建议分离，便于权限控制和维护。
- 推荐所有源码均采用 TypeScript，提升类型安全和开发效率。
- 目录结构可根据实际业务扩展，但应保持清晰、分层合理。

