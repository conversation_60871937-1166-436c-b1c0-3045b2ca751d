---
description: 
globs: 
alwaysApply: true
---
# SEO优化规则

本规则适用于企业POS产品展示网站，旨在提升搜索引擎友好度和自然流量。

## 1. 前端SEO优化
- 所有页面需设置唯一且有意义的 `<title>`、`<meta name="description">`、`<meta name="keywords">`。
- 使用语义化HTML标签（如 `<header>`、`<nav>`、`<main>`、`<section>`、`<footer>` 等）。
- 图片需设置 `alt` 属性，描述内容相关信息。
- 重要内容优先加载，减少首屏渲染时间。
- 支持移动端自适应（响应式设计）。
- 使用 [vue-meta](mdc:tech-stack.mdc) 或等效方案动态管理页面元信息。

## 2. 后端SEO优化
- 支持服务端渲染（SSR）或预渲染（如 Nuxt、VitePress、Prerender）。
- 提供 sitemap.xml 和 robots.txt。
- URL结构应简洁、可读，避免动态参数过多。
- 支持 301/302 跳转，避免死链。

## 3. 内容与结构优化
- 文章、产品、公司简介等内容应定期更新，保持活跃度。
- 关键词布局自然，避免堆砌。
- 支持结构化数据（JSON-LD、Open Graph、Twitter Card等）。
- 重要页面应有内链，提升爬虫抓取效率。

## 4. 性能与安全
- 静态资源开启Gzip/Brotli压缩。
- 图片、脚本、样式等资源按需加载，支持懒加载。
- 使用HTTPS，避免安全警告。

## 相关文件引用
- [项目结构说明](mdc:project-structure.mdc)
- [UI与交互设计规范](mdc:ui-ux-guidelines.mdc)

