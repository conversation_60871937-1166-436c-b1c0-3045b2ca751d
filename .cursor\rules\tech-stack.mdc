---
description: 
globs: 
alwaysApply: true
---
# 技术栈与依赖管理

本规则适用于企业官网及管理后台的技术选型、依赖管理与版本建议。

## 1. 技术栈
- 前端：Vue 3.x + TypeScript + Vite + TailwindCSS
- 后端：Node.js (建议16.x及以上) + TypeScript + Express/Koa/Fastify（三选一）
- 管理后台：独立Vue 3.x + TypeScript项目，推荐Element Plus或Ant Design Vue
- 数据库：MySQL/PostgreSQL（推荐使用ORM如Prisma/TypeORM）
- 其他：Nginx（反向代理）、PM2（进程管理）、Git（版本控制）

## 2. 依赖管理
- 统一使用pnpm/yarn/npm进行依赖管理，推荐pnpm
- 所有依赖需锁定版本，避免生产环境不一致
- 依赖升级需先在测试环境验证，避免破坏兼容性

## 3. 版本建议
- Node.js：16.x及以上
- Vue：3.x
- TypeScript：4.x及以上
- TailwindCSS：3.x及以上
- Element Plus/Ant Design Vue：最新稳定版

## 4. 其他建议
- 推荐使用ESLint、Prettier进行代码规范和格式化
- 建议配置Husky+lint-staged实现提交前自动检查
- 依赖包定期安全审计（npm audit/pnpm audit）

## 相关文件引用
- [项目结构说明](mdc:project-structure.mdc)
- [UI与交互设计规范](mdc:ui-ux-guidelines.mdc)

