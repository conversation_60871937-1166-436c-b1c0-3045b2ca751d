---
description: 
globs: 
alwaysApply: true
---
# UI与交互设计规范

本规则适用于企业官网及管理后台的界面设计、用户体验与交互规范。

## 1. 设计风格
- 采用现代化、简洁、专业的设计风格，符合企业品牌形象
- 主色调、辅助色、字体、按钮等需统一，体现品牌一致性
- 支持深色/浅色主题切换（如有需求）

## 2. 响应式与可用性
- 全站响应式设计，兼容PC、平板、手机等主流设备
- 重要操作按钮需高亮，交互流程简洁明了
- 表单输入需有清晰的校验与错误提示
- 支持键盘操作与无障碍访问（a11y）

## 3. 组件与样式
- 推荐使用TailwindCSS进行原子化样式开发，提升开发效率
- 组件库建议选用Element Plus、Ant Design Vue等，保持一致性
- 图片、图标需优化压缩，保证加载速度

## 4. 动效与反馈
- 适当使用动效提升用户体验，但避免过度炫技
- 所有操作需有明确反馈（如加载、成功、失败提示）

## 5. 其他建议
- 保持页面层级清晰，导航结构合理
- 重要信息突出展示，弱化次要内容
- 支持多语言（如有需求）

## 相关文件引用
- [SEO优化规则](mdc:seo-guidelines.mdc)
- [项目结构说明](mdc:project-structure.mdc)

