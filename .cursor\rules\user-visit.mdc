---
description: 
globs: 
alwaysApply: true
---
# 用户访问与数据采集规则

本规则适用于企业官网用户访问数据的采集、存储与合规管理。

## 1. 用户访问表设计
- 访问ID（唯一标识）
- 访问时间
- 用户IP地址
- 访问页面URL
- 来源渠道（Referer）
- 设备信息（User-Agent、操作系统、浏览器等）
- 访问行为（如点击、停留时长、表单提交等）
- 可选：地理位置、访问深度、会话ID

## 2. 数据采集建议
- 前端埋点采集用户行为，后端API统一接收并存储。
- 采集脚本应异步加载，避免影响页面性能。
- 重要事件（如表单提交、产品咨询）需单独记录。
- 支持数据脱敏与匿名化处理。

## 3. 隐私与合规
- 明确告知用户数据采集用途，必要时提供隐私政策页面。
- 遵守相关法律法规（如《个人信息保护法》）。
- 用户敏感信息需加密存储，严禁泄露。

## 相关文件引用
- [项目结构说明](mdc:project-structure.mdc)
- [管理后台开发规范](mdc:admin-panel.mdc)

