# 部署指南

本文档详细介绍如何在宝塔面板LNMP环境中部署POS企业网站。

## 环境要求

### 服务器环境
- **操作系统**: CentOS 7+ / Ubuntu 18.04+
- **内存**: 2GB以上
- **硬盘**: 20GB以上可用空间
- **宝塔面板**: 7.0+

### 软件环境
- **Node.js**: 16.x 或更高版本
- **MySQL**: 5.7 或更高版本
- **Nginx**: 1.18+
- **PM2**: 进程管理器

## 部署步骤

### 1. 准备工作

#### 1.1 安装Node.js
```bash
# 通过宝塔面板软件商店安装Node.js 16+
# 或者手动安装
curl -fsSL https://rpm.nodesource.com/setup_16.x | sudo bash -
sudo yum install -y nodejs

# 验证安装
node -v
npm -v
```

#### 1.2 安装PM2
```bash
npm install -g pm2
```

#### 1.3 创建MySQL数据库
在宝塔面板中创建数据库：
- 数据库名: `pos_website`
- 用户名: `pos_user`
- 密码: `your_secure_password`

### 2. 代码部署

#### 2.1 上传代码
```bash
# 进入网站根目录
cd /www/wwwroot/your-domain.com

# 克隆代码（或上传压缩包）
git clone <your-repository-url> .

# 或者解压上传的代码包
# unzip pos-website.zip
```

#### 2.2 安装依赖
```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 运行部署脚本
./deploy.sh
```

或者手动安装：
```bash
# 安装根目录依赖
npm install

# 安装各模块依赖
cd shared && npm install && npm run build && cd ..
cd backend && npm install && cd ..
cd frontend && npm install && cd ..
cd admin && npm install && cd ..
```

### 3. 配置环境

#### 3.1 配置后端环境变量
```bash
cd backend
cp .env.example .env
vim .env
```

编辑`.env`文件：
```env
# 数据库配置
DATABASE_URL="mysql://pos_user:your_secure_password@localhost:3306/pos_website"

# JWT密钥（请生成一个安全的密钥）
JWT_SECRET="your-super-secret-jwt-key-here-make-it-long-and-random"
JWT_EXPIRES_IN="7d"

# 服务器配置
PORT=3001
NODE_ENV="production"

# 文件上传配置
UPLOAD_DIR="uploads"
MAX_FILE_SIZE=5242880

# CORS配置
FRONTEND_URL="https://your-domain.com"
ADMIN_URL="https://your-domain.com/admin"
```

#### 3.2 初始化数据库
```bash
cd backend

# 生成Prisma客户端
npm run db:generate

# 推送数据库结构
npm run db:push

# 初始化示例数据
npm run db:init
```

### 4. 构建项目

#### 4.1 构建所有模块
```bash
# 返回根目录
cd /www/wwwroot/your-domain.com

# 构建共享模块
cd shared && npm run build && cd ..

# 构建后端
cd backend && npm run build && cd ..

# 构建前端
cd frontend && npm run build && cd ..

# 构建管理后台
cd admin && npm run build && cd ..
```

### 5. 配置Nginx

#### 5.1 创建Nginx配置文件
在宝塔面板中，为您的网站添加以下Nginx配置：

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # 强制HTTPS（可选）
    # return 301 https://$server_name$request_uri;
    
    # 前端静态文件
    location / {
        root /www/wwwroot/your-domain.com/frontend/.output/public;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # 后端API
    location /api {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # 管理后台
    location /admin {
        alias /www/wwwroot/your-domain.com/admin/dist;
        try_files $uri $uri/ /index.html;
        
        # 基本认证（可选，增加安全性）
        # auth_basic "Admin Area";
        # auth_basic_user_file /etc/nginx/.htpasswd;
    }
    
    # 文件上传
    location /uploads {
        alias /www/wwwroot/your-domain.com/backend/uploads;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # 安全设置
    location ~ /\. {
        deny all;
    }
    
    location ~ \.(sql|log|env)$ {
        deny all;
    }
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 文件上传大小限制
    client_max_body_size 10M;
}

# HTTPS配置（如果使用SSL证书）
server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL证书配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL安全设置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_prefer_server_ciphers on;
    
    # 其他配置与HTTP相同...
}
```

### 6. 启动服务

#### 6.1 使用PM2启动后端服务
```bash
cd /www/wwwroot/your-domain.com

# 启动后端服务
pm2 start ecosystem.config.js

# 设置开机自启
pm2 startup
pm2 save
```

#### 6.2 验证服务状态
```bash
# 查看PM2状态
pm2 status

# 查看日志
pm2 logs pos-backend

# 重启服务
pm2 restart pos-backend
```

### 7. 测试部署

#### 7.1 测试后端API
```bash
curl http://localhost:3001/api/health
```

#### 7.2 测试前端访问
访问 `http://your-domain.com` 检查前端是否正常显示

#### 7.3 测试管理后台
访问 `http://your-domain.com/admin` 并使用以下默认账号登录：
- 用户名: `admin`
- 密码: `admin123`

**重要**: 登录后请立即修改默认密码！

## 维护和监控

### 日常维护命令
```bash
# 查看服务状态
pm2 status

# 查看日志
pm2 logs pos-backend

# 重启服务
pm2 restart pos-backend

# 更新代码后重新部署
git pull
npm run build
pm2 restart pos-backend

# 数据库备份
mysqldump -u pos_user -p pos_website > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 性能优化
1. 启用Nginx Gzip压缩
2. 配置静态资源缓存
3. 使用CDN加速静态资源
4. 定期清理日志文件
5. 监控服务器资源使用情况

### 安全建议
1. 定期更新系统和软件包
2. 使用强密码和JWT密钥
3. 启用HTTPS
4. 配置防火墙规则
5. 定期备份数据库
6. 监控异常访问日志

## 故障排除

### 常见问题

#### 1. 后端服务启动失败
- 检查数据库连接配置
- 检查端口是否被占用
- 查看PM2日志: `pm2 logs pos-backend`

#### 2. 前端页面无法访问
- 检查Nginx配置
- 确认前端构建是否成功
- 检查文件权限

#### 3. 管理后台登录失败
- 确认数据库初始化是否成功
- 检查JWT密钥配置
- 查看后端API日志

#### 4. 文件上传失败
- 检查uploads目录权限
- 确认Nginx文件大小限制
- 检查磁盘空间

### 获取帮助
如果遇到问题，请：
1. 查看相关日志文件
2. 检查配置文件
3. 参考项目文档
4. 联系技术支持

## 更新升级

### 代码更新流程
```bash
# 1. 备份当前版本
cp -r /www/wwwroot/your-domain.com /backup/pos-website-$(date +%Y%m%d)

# 2. 拉取最新代码
git pull

# 3. 安装新依赖
npm install
cd frontend && npm install && cd ..
cd backend && npm install && cd ..
cd admin && npm install && cd ..

# 4. 运行数据库迁移（如有）
cd backend && npm run db:migrate && cd ..

# 5. 重新构建
npm run build

# 6. 重启服务
pm2 restart pos-backend

# 7. 测试功能
curl http://localhost:3001/api/health
```

### 数据库迁移
```bash
cd backend

# 创建迁移文件
npx prisma migrate dev --name migration_name

# 应用迁移
npx prisma migrate deploy
```
