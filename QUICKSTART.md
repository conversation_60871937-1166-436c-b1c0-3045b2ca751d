# 快速开始指南

本指南将帮助您快速搭建和运行POS企业网站项目。

## 🚀 一键部署（推荐）

### 在宝塔面板中部署

1. **上传代码到服务器**
   ```bash
   # 进入网站目录
   cd /www/wwwroot/your-domain.com
   
   # 上传并解压代码包
   # 或者使用git克隆
   ```

2. **运行一键部署脚本**
   ```bash
   chmod +x deploy.sh
   ./deploy.sh
   ```

3. **配置数据库连接**
   ```bash
   cd backend
   cp .env.example .env
   # 编辑.env文件，配置数据库连接
   ```

4. **初始化数据库**
   ```bash
   npm run db:push
   npm run db:init
   ```

5. **启动服务**
   ```bash
   pm2 start ecosystem.config.js
   ```

## 🛠️ 手动部署

### 环境准备

确保您的服务器已安装：
- Node.js 16+
- MySQL 5.7+
- Nginx
- PM2

### 步骤1: 安装依赖

```bash
# 根目录
npm install

# 各模块依赖
cd shared && npm install && npm run build && cd ..
cd backend && npm install && cd ..
cd frontend && npm install && cd ..
cd admin && npm install && cd ..
```

### 步骤2: 配置环境

```bash
# 配置后端环境变量
cd backend
cp .env.example .env
# 编辑.env文件，配置数据库连接和其他设置
```

### 步骤3: 数据库设置

```bash
cd backend

# 生成Prisma客户端
npm run db:generate

# 推送数据库结构
npm run db:push

# 初始化示例数据
npm run db:init
```

### 步骤4: 构建项目

```bash
# 构建所有模块
npm run build
```

### 步骤5: 配置Nginx

使用提供的nginx.conf.example配置文件，或参考DEPLOYMENT.md中的详细配置。

### 步骤6: 启动服务

```bash
# 启动后端服务
pm2 start ecosystem.config.js

# 设置开机自启
pm2 startup
pm2 save
```

## 🧪 开发环境

### 本地开发

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd pos-website
   ```

2. **安装依赖**
   ```bash
   npm run install:all
   ```

3. **配置环境**
   ```bash
   cd backend
   cp .env.example .env
   # 配置本地数据库连接
   ```

4. **初始化数据库**
   ```bash
   cd backend
   npm run db:push
   npm run db:init
   ```

5. **启动开发服务器**
   ```bash
   # 根目录，同时启动所有服务
   npm run dev
   
   # 或者分别启动
   npm run dev:backend   # 后端 http://localhost:3001
   npm run dev:frontend  # 前端 http://localhost:3000
   npm run dev:admin     # 管理后台 http://localhost:3002
   ```

## 📋 默认账号信息

### 管理后台登录
- **URL**: http://your-domain.com/admin
- **用户名**: admin
- **密码**: admin123

**⚠️ 重要**: 首次登录后请立即修改默认密码！

## 🔧 常用命令

### 开发命令
```bash
# 启动开发环境
npm run dev

# 构建项目
npm run build

# 安装所有依赖
npm run install:all
```

### 数据库命令
```bash
cd backend

# 生成Prisma客户端
npm run db:generate

# 推送数据库结构
npm run db:push

# 创建迁移
npm run db:migrate

# 初始化数据
npm run db:init

# 重置数据库
npm run db:reset

# 打开数据库管理界面
npm run db:studio
```

### PM2命令
```bash
# 查看服务状态
pm2 status

# 查看日志
pm2 logs pos-backend

# 重启服务
pm2 restart pos-backend

# 停止服务
pm2 stop pos-backend

# 删除服务
pm2 delete pos-backend
```

## 📁 项目结构

```
/
├── backend/          # Node.js 后端API
│   ├── src/         # 源代码
│   ├── prisma/      # 数据库模型
│   └── uploads/     # 文件上传目录
├── frontend/         # Nuxt.js 企业官网
│   ├── components/  # Vue组件
│   ├── pages/       # 页面
│   └── assets/      # 静态资源
├── admin/           # Vue.js 管理后台
│   ├── src/         # 源代码
│   └── dist/        # 构建输出
├── shared/          # 共享类型定义
└── rules/           # 项目规范文档
```

## 🌐 访问地址

### 生产环境
- **企业官网**: https://your-domain.com
- **管理后台**: https://your-domain.com/admin
- **API接口**: https://your-domain.com/api

### 开发环境
- **企业官网**: http://localhost:3000
- **管理后台**: http://localhost:3002
- **API接口**: http://localhost:3001/api

## 📝 功能清单

### ✅ 已实现功能

#### 企业官网
- [x] 响应式设计
- [x] SEO优化
- [x] 首页展示
- [x] 产品展示
- [x] 新闻文章
- [x] 联系表单
- [x] 访问统计

#### 管理后台
- [x] 用户认证
- [x] 公司信息管理
- [x] 产品管理
- [x] 文章管理
- [x] 联系表单管理
- [x] 访问数据统计
- [x] SEO设置
- [x] 文件上传

#### 后端API
- [x] RESTful API
- [x] JWT认证
- [x] 数据验证
- [x] 错误处理
- [x] 文件上传
- [x] 访问统计

## 🔍 测试验证

### 功能测试
1. **前端访问**: 访问首页，检查页面显示
2. **产品展示**: 查看产品列表和详情页
3. **联系表单**: 提交联系表单
4. **管理后台**: 登录管理后台
5. **内容管理**: 添加/编辑产品和文章
6. **文件上传**: 测试图片上传功能

### API测试
```bash
# 健康检查
curl http://localhost:3001/api/health

# 获取公司信息
curl http://localhost:3001/api/company

# 获取产品列表
curl http://localhost:3001/api/products
```

## 🆘 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   netstat -tlnp | grep :3001
   
   # 杀死进程
   kill -9 <PID>
   ```

2. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 验证数据库连接配置
   - 确认数据库用户权限

3. **权限问题**
   ```bash
   # 设置文件权限
   chmod -R 755 /www/wwwroot/your-domain.com
   chown -R www:www /www/wwwroot/your-domain.com
   ```

4. **依赖安装失败**
   ```bash
   # 清除缓存重新安装
   npm cache clean --force
   rm -rf node_modules package-lock.json
   npm install
   ```

## 📞 技术支持

如果您在部署过程中遇到问题，请：

1. 查看相关日志文件
2. 检查配置是否正确
3. 参考详细文档 DEPLOYMENT.md
4. 联系技术支持团队

## 🔄 下一步

部署成功后，建议您：

1. **修改默认密码**
2. **配置SSL证书**
3. **设置定期备份**
4. **配置监控告警**
5. **优化SEO设置**
6. **添加企业内容**

祝您使用愉快！🎉
