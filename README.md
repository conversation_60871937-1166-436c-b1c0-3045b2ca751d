# POS企业网站

专业的POS产品展示与宣传网站，采用现代化技术栈开发，支持SEO优化、内容管理和用户数据收集。

## 技术栈

### 前端（企业官网）
- **Nuxt.js 3** - Vue.js全栈框架，支持SSR/SSG
- **Vue 3** - 渐进式JavaScript框架
- **TypeScript** - 类型安全的JavaScript
- **TailwindCSS** - 原子化CSS框架
- **Pinia** - Vue状态管理

### 后端API
- **Node.js** - JavaScript运行时
- **Express** - Web应用框架
- **TypeScript** - 类型安全
- **Prisma** - 现代化ORM
- **MySQL** - 关系型数据库

### 管理后台
- **Vue 3** - 前端框架
- **Element Plus** - UI组件库
- **TypeScript** - 类型安全
- **Vite** - 构建工具

## 项目结构

```
/
├── backend/          # Node.js 后端API
├── frontend/         # Nuxt.js 企业官网
├── admin/           # Vue.js 管理后台
├── shared/          # 共享类型定义
├── rules/           # 项目规范文档
└── package.json     # 根目录配置
```

## 功能特性

### 企业官网
- ✅ SEO友好的服务端渲染
- ✅ 响应式设计，支持移动端
- ✅ 企业信息展示
- ✅ 产品展示与分类
- ✅ 新闻文章系统
- ✅ 用户咨询表单
- ✅ 访问数据统计

### 管理后台
- ✅ 用户认证与权限管理
- ✅ 公司信息管理
- ✅ 产品管理（CRUD）
- ✅ 文章管理（CRUD）
- ✅ 用户访问数据统计
- ✅ SEO设置管理
- ✅ 文件上传管理

## 快速开始

### 环境要求
- Node.js 16.x 或更高版本
- MySQL 5.7 或更高版本
- pnpm/npm/yarn 包管理器

### 安装依赖
```bash
# 安装所有项目依赖
npm run install:all

# 或者分别安装
npm install
cd frontend && npm install
cd ../backend && npm install
cd ../admin && npm install
cd ../shared && npm install
```

### 环境配置
```bash
# 复制环境变量文件
cp backend/.env.example backend/.env

# 编辑环境变量
vim backend/.env
```

### 数据库设置
```bash
# 进入后端目录
cd backend

# 生成Prisma客户端
npm run db:generate

# 推送数据库结构
npm run db:push

# 或者使用迁移（推荐生产环境）
npm run db:migrate
```

### 启动开发服务器
```bash
# 启动所有服务（根目录）
npm run dev

# 或者分别启动
npm run dev:frontend  # 前端 http://localhost:3000
npm run dev:backend   # 后端 http://localhost:3001
npm run dev:admin     # 管理后台 http://localhost:3002
```

## 部署指南

### 宝塔面板部署

1. **环境准备**
   - 安装Node.js 16+
   - 安装MySQL
   - 配置Nginx

2. **代码部署**
   ```bash
   # 克隆代码到服务器
   git clone <repository-url> /www/wwwroot/pos-website
   cd /www/wwwroot/pos-website
   
   # 安装依赖
   npm run install:all
   
   # 构建项目
   npm run build
   ```

3. **数据库配置**
   ```bash
   # 配置环境变量
   cp backend/.env.example backend/.env
   
   # 设置数据库连接
   # 运行数据库迁移
   cd backend && npm run db:push
   ```

4. **启动服务**
   ```bash
   # 使用PM2管理进程
   pm2 start backend/dist/index.js --name "pos-backend"
   pm2 startup
   pm2 save
   ```

5. **Nginx配置**
   ```nginx
   # 前端静态文件
   location / {
       root /www/wwwroot/pos-website/frontend/.output/public;
       try_files $uri $uri/ /index.html;
   }
   
   # 后端API代理
   location /api {
       proxy_pass http://localhost:3001;
       proxy_set_header Host $host;
       proxy_set_header X-Real-IP $remote_addr;
   }
   
   # 管理后台
   location /admin {
       root /www/wwwroot/pos-website/admin/dist;
       try_files $uri $uri/ /index.html;
   }
   ```

## 开发规范

详细的开发规范请参考 `rules/` 目录下的文档：

- [项目结构说明](./rules/project-structure.mdc)
- [技术栈与依赖管理](./rules/tech-stack.mdc)
- [SEO优化规则](./rules/seo-guidelines.mdc)
- [UI与交互设计规范](./rules/ui-ux-guidelines.mdc)
- [企业信息与内容管理规则](./rules/company-content.mdc)
- [产品展示与管理规则](./rules/product-management.mdc)
- [用户访问与数据采集规则](./rules/user-visit.mdc)
- [管理后台开发规范](./rules/admin-panel.mdc)

## 许可证

本项目采用 MIT 许可证。
