# POS企业网站 - Web安装指南

## 🎯 概述

我们为您创建了一个类似WordPress的Web安装向导，让您可以通过浏览器轻松完成整个网站的安装配置，无需使用命令行。

## 🚀 安装步骤

### 第1步：上传文件

1. **下载项目文件**
   - 将整个项目文件夹下载到本地

2. **上传到宝塔面板**
   - 登录宝塔面板：http://*************:280/
   - 进入"网站"管理
   - 选择您的网站目录（如：/www/wwwroot/your-domain/）
   - 上传项目文件并解压

### 第2步：访问安装向导

在浏览器中访问：
```
http://*************/install/
```

### 第3步：按向导完成安装

#### 步骤1：环境检查
- 系统会自动检查服务器环境
- 检查Node.js版本（需要16+）
- 检查NPM版本
- 检查文件写入权限
- 检查端口可用性

#### 步骤2：数据库配置
- **数据库主机**：localhost
- **端口**：3306
- **数据库名称**：pos_website（或您自定义的名称）
- **用户名**：您的MySQL用户名
- **密码**：您的MySQL密码

点击"测试连接"确保数据库配置正确。

#### 步骤3：网站配置
- **网站名称**：您的企业名称
- **网站地址**：http://*************
- **网站描述**：企业简介
- **联系电话**：企业电话
- **联系邮箱**：企业邮箱
- **公司地址**：企业地址

#### 步骤4：管理员账号
- **用户名**：管理后台登录用户名
- **邮箱**：管理员邮箱
- **密码**：管理员密码（至少6位）
- **确认密码**：再次输入密码

#### 步骤5：自动安装
- 系统会自动执行以下操作：
  - 安装项目依赖
  - 配置数据库连接
  - 初始化数据库结构
  - 创建示例数据
  - 构建项目文件
  - 启动后端服务

## 🎉 安装完成

安装成功后，您将看到：

- **企业官网地址**：http://*************
- **管理后台地址**：http://*************/admin
- **管理员账号信息**

## 🔧 宝塔面板配置

### 1. Node.js环境
确保在宝塔面板中安装了Node.js管理器：
- 软件商店 → Node.js版本管理器
- 安装Node.js 16.x或更高版本

### 2. MySQL数据库
在宝塔面板中创建数据库：
- 数据库 → 添加数据库
- 数据库名：pos_website
- 用户名：pos_user
- 密码：设置一个安全密码

### 3. 网站设置
在宝塔面板中配置网站：
- 网站 → 设置 → 配置文件
- 添加以下Nginx配置：

```nginx
# 前端静态文件
location / {
    root /www/wwwroot/your-domain/frontend/.output/public;
    try_files $uri $uri/ /index.html;
}

# 后端API
location /api {
    proxy_pass http://localhost:3001;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# 管理后台
location /admin {
    alias /www/wwwroot/your-domain/admin/dist;
    try_files $uri $uri/ /index.html;
}

# 文件上传
location /uploads {
    alias /www/wwwroot/your-domain/backend/uploads;
    expires 1y;
    add_header Cache-Control "public";
}
```

## 🛠️ 功能特点

### Web安装向导特点
- ✅ 可视化安装界面
- ✅ 自动环境检查
- ✅ 实时数据库连接测试
- ✅ 自动依赖安装
- ✅ 自动服务启动
- ✅ 防重复安装保护

### 安装后功能
- ✅ 企业官网（SEO优化）
- ✅ 产品展示系统
- ✅ 新闻文章管理
- ✅ 用户访问统计
- ✅ 联系表单收集
- ✅ 管理后台
- ✅ 文件上传管理

## 🔍 故障排除

### 常见问题

#### 1. 无法访问安装页面
- 检查文件是否正确上传
- 确保install目录存在
- 检查网站是否正常运行

#### 2. 环境检查失败
- 在宝塔面板安装Node.js管理器
- 确保Node.js版本 >= 16
- 检查目录权限设置

#### 3. 数据库连接失败
- 确保MySQL服务正常运行
- 检查数据库用户名密码
- 确认数据库用户有足够权限

#### 4. 安装过程中断
- 检查服务器磁盘空间
- 确保网络连接稳定
- 查看宝塔面板错误日志

## 📱 移动端支持

安装向导完全支持移动设备访问，您可以在手机或平板上完成安装。

## 🔒 安全提示

1. **安装完成后删除install目录**
2. **修改默认管理员密码**
3. **启用HTTPS（推荐）**
4. **定期备份数据库**
5. **保持系统更新**

## 📞 技术支持

如果在安装过程中遇到问题：

1. 查看浏览器控制台错误信息
2. 检查宝塔面板系统日志
3. 参考项目根目录的详细文档
4. 联系技术支持团队

---

**祝您安装顺利！** 🎉

通过Web安装向导，您可以在几分钟内完成整个POS企业网站的部署，无需任何技术背景。
