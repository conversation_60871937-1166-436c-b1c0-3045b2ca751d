import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
      meta: { requiresAuth: false }
    },
    {
      path: '/',
      component: () => import('@/layouts/AdminLayout.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          name: 'Dashboard',
          component: () => import('@/views/Dashboard.vue')
        },
        {
          path: 'company',
          name: 'Company',
          component: () => import('@/views/Company.vue')
        },
        {
          path: 'products',
          name: 'Products',
          component: () => import('@/views/Products.vue')
        },
        {
          path: 'products/create',
          name: 'ProductCreate',
          component: () => import('@/views/ProductForm.vue')
        },
        {
          path: 'products/:id/edit',
          name: 'ProductEdit',
          component: () => import('@/views/ProductForm.vue')
        },
        {
          path: 'articles',
          name: 'Articles',
          component: () => import('@/views/Articles.vue')
        },
        {
          path: 'articles/create',
          name: 'ArticleCreate',
          component: () => import('@/views/ArticleForm.vue')
        },
        {
          path: 'articles/:id/edit',
          name: 'ArticleEdit',
          component: () => import('@/views/ArticleForm.vue')
        },
        {
          path: 'contacts',
          name: 'Contacts',
          component: () => import('@/views/Contacts.vue')
        },
        {
          path: 'analytics',
          name: 'Analytics',
          component: () => import('@/views/Analytics.vue')
        },
        {
          path: 'seo',
          name: 'SEO',
          component: () => import('@/views/SEO.vue')
        },
        {
          path: 'users',
          name: 'Users',
          component: () => import('@/views/Users.vue'),
          meta: { roles: ['SUPER_ADMIN'] }
        },
        {
          path: 'settings',
          name: 'Settings',
          component: () => import('@/views/Settings.vue')
        }
      ]
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/NotFound.vue')
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  // 检查是否需要认证
  if (to.meta.requiresAuth !== false) {
    if (!userStore.isAuthenticated) {
      next('/login')
      return
    }
    
    // 检查角色权限
    if (to.meta.roles && Array.isArray(to.meta.roles)) {
      if (!to.meta.roles.includes(userStore.user?.role)) {
        ElMessage.error('权限不足')
        next('/')
        return
      }
    }
  }
  
  // 如果已登录且访问登录页，重定向到首页
  if (to.name === 'Login' && userStore.isAuthenticated) {
    next('/')
    return
  }
  
  next()
})

export default router
