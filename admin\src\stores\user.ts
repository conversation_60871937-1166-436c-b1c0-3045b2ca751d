import { defineStore } from 'pinia'
import { api } from '@/utils/api'
import Cookies from 'js-cookie'

interface User {
  id: string
  username: string
  email: string
  role: 'SUPER_ADMIN' | 'ADMIN' | 'EDITOR'
  lastLoginAt?: string
  createdAt: string
}

interface UserState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    user: null,
    token: Cookies.get('admin_token') || null,
    isAuthenticated: false
  }),

  getters: {
    isAdmin: (state) => {
      return state.user?.role === 'SUPER_ADMIN' || state.user?.role === 'ADMIN'
    },
    isSuperAdmin: (state) => {
      return state.user?.role === 'SUPER_ADMIN'
    }
  },

  actions: {
    // 登录
    async login(credentials: { username: string; password: string }) {
      try {
        const response = await api.post('/admin/login', credentials)
        
        if (response.data.success) {
          const { token, user } = response.data.data
          
          this.token = token
          this.user = user
          this.isAuthenticated = true
          
          // 保存token到cookie
          Cookies.set('admin_token', token, { expires: 7 })
          
          // 设置axios默认header
          api.defaults.headers.common['Authorization'] = `Bearer ${token}`
          
          return { success: true }
        } else {
          return { success: false, error: response.data.error }
        }
      } catch (error: any) {
        console.error('登录失败:', error)
        return { 
          success: false, 
          error: error.response?.data?.error || '登录失败' 
        }
      }
    },

    // 登出
    logout() {
      this.user = null
      this.token = null
      this.isAuthenticated = false
      
      // 清除cookie
      Cookies.remove('admin_token')
      
      // 清除axios header
      delete api.defaults.headers.common['Authorization']
      
      // 跳转到登录页
      this.$router?.push('/login')
    },

    // 检查认证状态
    async checkAuth() {
      if (!this.token) {
        this.isAuthenticated = false
        return false
      }

      try {
        // 设置axios header
        api.defaults.headers.common['Authorization'] = `Bearer ${this.token}`
        
        const response = await api.get('/admin/me')
        
        if (response.data.success) {
          this.user = response.data.data
          this.isAuthenticated = true
          return true
        } else {
          this.logout()
          return false
        }
      } catch (error) {
        console.error('认证检查失败:', error)
        this.logout()
        return false
      }
    },

    // 修改密码
    async changePassword(data: {
      currentPassword: string
      newPassword: string
      confirmPassword: string
    }) {
      try {
        const response = await api.put('/admin/password', data)
        
        if (response.data.success) {
          ElMessage.success('密码修改成功')
          return { success: true }
        } else {
          ElMessage.error(response.data.error)
          return { success: false, error: response.data.error }
        }
      } catch (error: any) {
        const errorMsg = error.response?.data?.error || '密码修改失败'
        ElMessage.error(errorMsg)
        return { success: false, error: errorMsg }
      }
    },

    // 创建用户（仅超级管理员）
    async createUser(userData: {
      username: string
      email: string
      password: string
      role: string
    }) {
      try {
        const response = await api.post('/admin/users', userData)
        
        if (response.data.success) {
          ElMessage.success('用户创建成功')
          return { success: true, data: response.data.data }
        } else {
          ElMessage.error(response.data.error)
          return { success: false, error: response.data.error }
        }
      } catch (error: any) {
        const errorMsg = error.response?.data?.error || '用户创建失败'
        ElMessage.error(errorMsg)
        return { success: false, error: errorMsg }
      }
    }
  }
})
