// 全局样式文件

// 重置样式
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// Element Plus 样式覆盖
.el-menu {
  border-right: none !important;
}

.el-table {
  .el-table__header {
    th {
      background-color: #fafafa;
      color: #606266;
      font-weight: 500;
    }
  }
}

.el-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-button {
  border-radius: 6px;
}

.el-input {
  .el-input__wrapper {
    border-radius: 6px;
  }
}

.el-select {
  .el-input__wrapper {
    border-radius: 6px;
  }
}

.el-textarea {
  .el-textarea__inner {
    border-radius: 6px;
  }
}

// 工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 8px; }
.ml-2 { margin-left: 16px; }
.ml-3 { margin-left: 24px; }
.ml-4 { margin-left: 32px; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 8px; }
.mr-2 { margin-right: 16px; }
.mr-3 { margin-right: 24px; }
.mr-4 { margin-right: 32px; }

.p-0 { padding: 0; }
.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }
.p-4 { padding: 32px; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 8px; }
.pb-2 { padding-bottom: 16px; }
.pb-3 { padding-bottom: 24px; }
.pb-4 { padding-bottom: 32px; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 8px; }
.pt-2 { padding-top: 16px; }
.pt-3 { padding-top: 24px; }
.pt-4 { padding-top: 32px; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 8px; }
.pl-2 { padding-left: 16px; }
.pl-3 { padding-left: 24px; }
.pl-4 { padding-left: 32px; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 8px; }
.pr-2 { padding-right: 16px; }
.pr-3 { padding-right: 24px; }
.pr-4 { padding-right: 32px; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.flex { display: flex; }
.flex-1 { flex: 1; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }

// 页面布局
.page-container {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
  
  .page-title {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin: 0 0 8px;
  }
  
  .page-description {
    color: #606266;
    margin: 0;
  }
}

.page-content {
  .el-card + .el-card {
    margin-top: 24px;
  }
}

// 表格操作
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  
  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .toolbar-right {
    display: flex;
    align-items: center;
    gap: 12px;
  }
}

// 状态标签
.status-tag {
  &.status-active {
    color: #67c23a;
    background-color: #f0f9ff;
    border-color: #67c23a;
  }
  
  &.status-inactive {
    color: #f56c6c;
    background-color: #fef0f0;
    border-color: #f56c6c;
  }
  
  &.status-pending {
    color: #e6a23c;
    background-color: #fdf6ec;
    border-color: #e6a23c;
  }
}

// 响应式
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .table-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    
    .toolbar-left,
    .toolbar-right {
      justify-content: center;
    }
  }
}
