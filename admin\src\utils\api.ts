import axios from 'axios'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

// 创建axios实例
export const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 显示加载进度条
    NProgress.start()
    
    // 从cookie获取token
    const token = document.cookie
      .split('; ')
      .find(row => row.startsWith('admin_token='))
      ?.split('=')[1]
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    NProgress.done()
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    NProgress.done()
    return response
  },
  (error) => {
    NProgress.done()
    
    // 处理错误响应
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('登录已过期，请重新登录')
          // 清除token并跳转到登录页
          document.cookie = 'admin_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
          window.location.href = '/login'
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 422:
          // 表单验证错误
          if (data.details && Array.isArray(data.details)) {
            const errorMsg = data.details.map((item: any) => item.msg).join(', ')
            ElMessage.error(errorMsg)
          } else {
            ElMessage.error(data.error || '数据验证失败')
          }
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data.error || '请求失败')
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败')
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// API方法封装
export const apiMethods = {
  // 公司信息
  getCompanyInfo: () => api.get('/company'),
  updateCompanyInfo: (data: any) => api.put('/company', data),

  // 产品管理
  getProducts: (params?: any) => api.get('/products', { params }),
  getProduct: (id: string) => api.get(`/products/${id}`),
  createProduct: (data: any) => api.post('/products', data),
  updateProduct: (id: string, data: any) => api.put(`/products/${id}`, data),
  deleteProduct: (id: string) => api.delete(`/products/${id}`),
  getProductCategories: () => api.get('/products/categories/list'),

  // 文章管理
  getArticles: (params?: any) => api.get('/articles', { params }),
  getArticle: (id: string) => api.get(`/articles/${id}`),
  createArticle: (data: any) => api.post('/articles', data),
  updateArticle: (id: string, data: any) => api.put(`/articles/${id}`, data),
  deleteArticle: (id: string) => api.delete(`/articles/${id}`),
  getArticleCategories: () => api.get('/articles/categories/list'),

  // 联系表单
  getContacts: (params?: any) => api.get('/contacts', { params }),
  updateContactStatus: (id: string, status: string) => api.put(`/contacts/${id}/status`, { status }),

  // 访问统计
  getVisitStats: (params?: any) => api.get('/visits/stats', { params }),

  // SEO设置
  getSeoSettings: () => api.get('/seo'),
  updateSeoSettings: (page: string, data: any) => api.put(`/seo/${page}`, data),

  // 文件上传
  uploadFile: (file: File, type?: string) => {
    const formData = new FormData()
    formData.append('file', file)
    return api.post('/upload/single', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
      params: { type }
    })
  },

  uploadFiles: (files: File[], type?: string) => {
    const formData = new FormData()
    files.forEach(file => formData.append('files', file))
    return api.post('/upload/multiple', formData, {
      headers: { 'Content-Type': 'multipart/form-data' },
      params: { type }
    })
  }
}

export default api
