{"name": "@pos-website/backend", "version": "1.0.0", "description": "POS企业网站后端API", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:init": "ts-node scripts/init-data.ts", "db:reset": "prisma db push --force-reset && npm run db:init"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "compression": "^1.7.4", "dotenv": "^16.3.1", "@prisma/client": "^5.7.1", "prisma": "^5.7.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/compression": "^1.7.5", "@types/node": "^20.10.5", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2"}}