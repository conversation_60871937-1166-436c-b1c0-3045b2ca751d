// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// 公司信息表
model CompanyInfo {
  id              String   @id @default(cuid())
  name            String
  logo            String?
  description     String   @db.Text
  foundedYear     Int
  address         String
  phone           String
  email           String
  website         String?
  businessLicense String?
  vision          String   @db.Text
  mission         String   @db.Text
  values          Json     // 存储字符串数组
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("company_info")
}

// 产品分类表
model ProductCategory {
  id          String    @id @default(cuid())
  name        String
  slug        String    @unique
  description String?   @db.Text
  parentId    String?
  sortOrder   Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  parent      ProductCategory? @relation("CategoryParent", fields: [parentId], references: [id])
  children    ProductCategory[] @relation("CategoryParent")
  products    Product[]

  @@map("product_categories")
}

// 产品表
model Product {
  id               String          @id @default(cuid())
  name             String
  slug             String          @unique
  description      String          @db.Text
  shortDescription String?         @db.Text
  features         Json            // 存储字符串数组
  specifications   Json            // 存储规格参数对象
  mainImage        String?
  images           Json            // 存储图片URL数组
  categoryId       String
  tags             Json            // 存储标签数组
  status           ProductStatus   @default(DRAFT)
  featured         Boolean         @default(false)
  sortOrder        Int             @default(0)
  seoTitle         String?
  seoDescription   String?         @db.Text
  seoKeywords      String?
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt
  
  category         ProductCategory @relation(fields: [categoryId], references: [id])

  @@map("products")
}

enum ProductStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

// 文章分类表
model ArticleCategory {
  id          String    @id @default(cuid())
  name        String
  slug        String    @unique
  description String?   @db.Text
  sortOrder   Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  articles    Article[]

  @@map("article_categories")
}

// 文章表
model Article {
  id             String          @id @default(cuid())
  title          String
  slug           String          @unique
  content        String          @db.LongText
  excerpt        String?         @db.Text
  featuredImage  String?
  categoryId     String
  tags           Json            // 存储标签数组
  status         ArticleStatus   @default(DRAFT)
  featured       Boolean         @default(false)
  sortOrder      Int             @default(0)
  authorId       String
  seoTitle       String?
  seoDescription String?         @db.Text
  seoKeywords    String?
  publishedAt    DateTime?
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  
  category       ArticleCategory @relation(fields: [categoryId], references: [id])
  author         AdminUser       @relation(fields: [authorId], references: [id])

  @@map("articles")
}

enum ArticleStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

// 用户访问记录表
model UserVisit {
  id           String   @id @default(cuid())
  sessionId    String
  ipAddress    String
  userAgent    String   @db.Text
  referer      String?  @db.Text
  url          String   @db.Text
  pageTitle    String
  visitTime    DateTime @default(now())
  stayDuration Int?     // 停留时长（秒）
  deviceType   String   // desktop, tablet, mobile
  browser      String
  os           String
  country      String?
  city         String?
  createdAt    DateTime @default(now())

  @@index([sessionId])
  @@index([visitTime])
  @@map("user_visits")
}

// 用户咨询表单表
model ContactForm {
  id        String            @id @default(cuid())
  name      String
  phone     String
  email     String?
  company   String?
  message   String            @db.Text
  source    String            // 来源页面
  status    ContactFormStatus @default(NEW)
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt

  @@map("contact_forms")
}

enum ContactFormStatus {
  NEW
  CONTACTED
  CLOSED
}

// 管理员用户表
model AdminUser {
  id          String          @id @default(cuid())
  username    String          @unique
  email       String          @unique
  password    String
  role        AdminUserRole   @default(EDITOR)
  status      AdminUserStatus @default(ACTIVE)
  lastLoginAt DateTime?
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  
  articles    Article[]

  @@map("admin_users")
}

enum AdminUserRole {
  SUPER_ADMIN
  ADMIN
  EDITOR
}

enum AdminUserStatus {
  ACTIVE
  INACTIVE
}

// SEO设置表
model SeoSettings {
  id            String   @id @default(cuid())
  page          String   @unique // 页面标识
  title         String
  description   String   @db.Text
  keywords      String   @db.Text
  ogTitle       String?
  ogDescription String?  @db.Text
  ogImage       String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@map("seo_settings")
}
