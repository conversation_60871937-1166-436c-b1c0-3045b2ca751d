import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🚀 开始初始化数据库数据...')

  try {
    // 创建默认管理员用户
    const hashedPassword = await bcrypt.hash('admin123', 12)
    
    const adminUser = await prisma.adminUser.upsert({
      where: { username: 'admin' },
      update: {},
      create: {
        username: 'admin',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'SUPER_ADMIN'
      }
    })
    console.log('✅ 创建默认管理员用户:', adminUser.username)

    // 创建默认公司信息
    const companyInfo = await prisma.companyInfo.upsert({
      where: { id: 'default' },
      update: {},
      create: {
        id: 'default',
        name: 'POS企业有限公司',
        description: '专业的POS产品解决方案提供商，致力于为企业提供高质量的收银设备、支付终端等产品，完善的售后服务，助力企业数字化转型。',
        foundedYear: 2010,
        address: '北京市朝阳区科技园区创新大厦A座1001室',
        phone: '************',
        email: '<EMAIL>',
        website: 'https://www.pos-company.com',
        vision: '成为全球领先的POS产品解决方案提供商',
        mission: '通过创新技术和优质服务，为客户创造价值',
        values: ['创新', '诚信', '专业', '服务']
      }
    })
    console.log('✅ 创建默认公司信息:', companyInfo.name)

    // 创建产品分类
    const categories = [
      { name: '收银机', slug: 'cash-register', description: '各类收银机产品' },
      { name: '支付终端', slug: 'payment-terminal', description: '支付终端设备' },
      { name: 'POS系统', slug: 'pos-system', description: 'POS软件系统' },
      { name: '配件耗材', slug: 'accessories', description: '相关配件和耗材' }
    ]

    for (const [index, category] of categories.entries()) {
      await prisma.productCategory.upsert({
        where: { slug: category.slug },
        update: {},
        create: {
          ...category,
          sortOrder: index + 1
        }
      })
    }
    console.log('✅ 创建产品分类:', categories.length, '个')

    // 创建示例产品
    const cashRegisterCategory = await prisma.productCategory.findUnique({
      where: { slug: 'cash-register' }
    })

    if (cashRegisterCategory) {
      const products = [
        {
          name: 'POS-2000 智能收银机',
          slug: 'pos-2000-smart-cash-register',
          description: '高性能智能收银机，支持多种支付方式，操作简单，性能稳定。适用于餐饮、零售、服务等各类商业场景。',
          shortDescription: '高性能智能收银机，支持多种支付方式',
          features: ['15寸触摸屏', '热敏打印机', '扫码支付', '会员管理', '库存管理'],
          specifications: {
            '屏幕尺寸': '15寸',
            '处理器': 'ARM Cortex-A53',
            '内存': '2GB',
            '存储': '16GB',
            '操作系统': 'Android 7.1',
            '打印机': '热敏打印机',
            '网络': 'WiFi + 4G'
          },
          categoryId: cashRegisterCategory.id,
          tags: ['智能', '触摸屏', '多支付'],
          status: 'PUBLISHED',
          featured: true,
          sortOrder: 1,
          seoTitle: 'POS-2000 智能收银机 - 专业收银解决方案',
          seoDescription: '高性能智能收银机，15寸触摸屏，支持扫码支付、会员管理、库存管理，适用于餐饮零售等商业场景',
          seoKeywords: 'POS收银机,智能收银机,触摸屏收银机,扫码支付'
        },
        {
          name: 'POS-1500 经济型收银机',
          slug: 'pos-1500-economic-cash-register',
          description: '经济实用的收银机产品，功能齐全，价格实惠，是中小商户的理想选择。',
          shortDescription: '经济实用的收银机产品，功能齐全，价格实惠',
          features: ['12寸显示屏', '热敏打印机', '现金抽屉', '基础收银功能'],
          specifications: {
            '屏幕尺寸': '12寸',
            '处理器': 'ARM Cortex-A7',
            '内存': '1GB',
            '存储': '8GB',
            '操作系统': 'Android 6.0',
            '打印机': '热敏打印机',
            '网络': 'WiFi'
          },
          categoryId: cashRegisterCategory.id,
          tags: ['经济型', '实用', '中小商户'],
          status: 'PUBLISHED',
          featured: false,
          sortOrder: 2,
          seoTitle: 'POS-1500 经济型收银机 - 中小商户首选',
          seoDescription: '经济实用的收银机产品，12寸显示屏，功能齐全价格实惠，中小商户的理想选择',
          seoKeywords: 'POS收银机,经济型收银机,中小商户,实用收银机'
        }
      ]

      for (const product of products) {
        await prisma.product.upsert({
          where: { slug: product.slug },
          update: {},
          create: product
        })
      }
      console.log('✅ 创建示例产品:', products.length, '个')
    }

    // 创建文章分类
    const articleCategories = [
      { name: '公司新闻', slug: 'company-news', description: '公司最新动态和新闻' },
      { name: '行业资讯', slug: 'industry-news', description: '行业相关资讯和趋势' },
      { name: '产品动态', slug: 'product-news', description: '产品更新和发布信息' },
      { name: '技术文章', slug: 'technical-articles', description: '技术相关文章和教程' }
    ]

    for (const [index, category] of articleCategories.entries()) {
      await prisma.articleCategory.upsert({
        where: { slug: category.slug },
        update: {},
        create: {
          ...category,
          sortOrder: index + 1
        }
      })
    }
    console.log('✅ 创建文章分类:', articleCategories.length, '个')

    // 创建示例文章
    const companyNewsCategory = await prisma.articleCategory.findUnique({
      where: { slug: 'company-news' }
    })

    if (companyNewsCategory) {
      const articles = [
        {
          title: '公司荣获"2023年度优秀POS设备供应商"称号',
          slug: 'company-awarded-excellent-pos-supplier-2023',
          content: `<p>近日，我公司在2023年度POS行业评选中荣获"优秀POS设备供应商"称号，这是对我们多年来在POS设备领域深耕细作的认可。</p>
          <p>自成立以来，我们始终坚持以客户需求为导向，不断创新产品技术，提升服务质量。此次获奖不仅是对我们过去工作的肯定，更是对未来发展的激励。</p>
          <p>未来，我们将继续秉承"创新、诚信、专业、服务"的企业价值观，为广大客户提供更优质的产品和服务。</p>`,
          excerpt: '我公司荣获2023年度优秀POS设备供应商称号，这是对我们多年来在POS设备领域深耕细作的认可。',
          categoryId: companyNewsCategory.id,
          tags: ['公司荣誉', '行业认可', '2023年度'],
          status: 'PUBLISHED',
          featured: true,
          sortOrder: 1,
          authorId: adminUser.id,
          publishedAt: new Date(),
          seoTitle: '公司荣获2023年度优秀POS设备供应商称号',
          seoDescription: '我公司在2023年度POS行业评选中荣获优秀POS设备供应商称号，体现了我们在POS设备领域的专业实力',
          seoKeywords: 'POS设备供应商,行业荣誉,2023年度,优秀供应商'
        }
      ]

      for (const article of articles) {
        await prisma.article.upsert({
          where: { slug: article.slug },
          update: {},
          create: article
        })
      }
      console.log('✅ 创建示例文章:', articles.length, '个')
    }

    // 创建默认SEO设置
    const seoPages = [
      {
        page: 'home',
        title: 'POS企业官网 - 专业的POS产品解决方案提供商',
        description: '专业的POS产品解决方案提供商，提供高质量的POS设备、收银机、支付终端等产品，完善的售后服务，助力企业数字化转型',
        keywords: 'POS,收银机,支付终端,企业解决方案,数字化转型,收银系统'
      },
      {
        page: 'products',
        title: '产品中心 - POS设备产品展示',
        description: '查看我们的POS产品系列，包括智能收银机、支付终端、POS系统等，为您的业务提供完整的收银解决方案',
        keywords: 'POS产品,收银机产品,支付终端,POS系统,收银解决方案'
      },
      {
        page: 'about',
        title: '关于我们 - 专业POS设备供应商',
        description: '了解我们的企业文化、发展历程和团队实力，我们致力于为客户提供专业的POS产品和服务',
        keywords: '关于我们,企业文化,POS设备供应商,公司简介'
      }
    ]

    for (const seo of seoPages) {
      await prisma.seoSettings.upsert({
        where: { page: seo.page },
        update: {},
        create: seo
      })
    }
    console.log('✅ 创建默认SEO设置:', seoPages.length, '个页面')

    console.log('🎉 数据库初始化完成！')
    console.log('')
    console.log('📋 默认管理员账号信息:')
    console.log('用户名: admin')
    console.log('密码: admin123')
    console.log('邮箱: <EMAIL>')
    console.log('')
    console.log('请及时修改默认密码！')

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
