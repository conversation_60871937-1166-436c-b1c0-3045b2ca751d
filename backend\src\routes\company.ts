import { Router } from 'express';
import { body, validationResult } from 'express-validator';
import prisma from '../lib/prisma';
import { authenticate, authorize } from '../middleware/auth';

const router = Router();

// 获取公司信息（公开接口）
router.get('/', async (req, res, next) => {
  try {
    const company = await prisma.companyInfo.findFirst();
    
    res.json({
      success: true,
      data: company
    });
  } catch (error) {
    next(error);
  }
});

// 更新公司信息（需要管理员权限）
router.put('/',
  authenticate,
  authorize(['SUPER_ADMIN', 'ADMIN']),
  [
    body('name').notEmpty().withMessage('公司名称不能为空'),
    body('description').notEmpty().withMessage('公司描述不能为空'),
    body('foundedYear').isInt({ min: 1900, max: new Date().getFullYear() }).withMessage('成立年份无效'),
    body('address').notEmpty().withMessage('公司地址不能为空'),
    body('phone').notEmpty().withMessage('联系电话不能为空'),
    body('email').isEmail().withMessage('邮箱格式无效'),
    body('vision').notEmpty().withMessage('企业愿景不能为空'),
    body('mission').notEmpty().withMessage('企业使命不能为空'),
    body('values').isArray().withMessage('企业价值观必须是数组')
  ],
  async (req, res, next) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: '数据验证失败',
          details: errors.array()
        });
      }

      const {
        name,
        logo,
        description,
        foundedYear,
        address,
        phone,
        email,
        website,
        businessLicense,
        vision,
        mission,
        values
      } = req.body;

      // 检查是否已存在公司信息
      const existingCompany = await prisma.companyInfo.findFirst();

      let company;
      if (existingCompany) {
        // 更新现有信息
        company = await prisma.companyInfo.update({
          where: { id: existingCompany.id },
          data: {
            name,
            logo,
            description,
            foundedYear,
            address,
            phone,
            email,
            website,
            businessLicense,
            vision,
            mission,
            values
          }
        });
      } else {
        // 创建新的公司信息
        company = await prisma.companyInfo.create({
          data: {
            name,
            logo,
            description,
            foundedYear,
            address,
            phone,
            email,
            website,
            businessLicense,
            vision,
            mission,
            values
          }
        });
      }

      res.json({
        success: true,
        data: company,
        message: '公司信息更新成功'
      });
    } catch (error) {
      next(error);
    }
  }
);

export default router;
