import { Router } from 'express';
import { body, validationResult } from 'express-validator';
import prisma from '../lib/prisma';
import { isValidPhone, isValidEmail } from '../../../shared/src/utils';

const router = Router();

// 提交联系表单（公开接口）
router.post('/', [
  body('name').notEmpty().withMessage('姓名不能为空'),
  body('phone').custom((value) => {
    if (!isValidPhone(value)) {
      throw new Error('手机号格式不正确');
    }
    return true;
  }),
  body('email').optional().custom((value) => {
    if (value && !isValidEmail(value)) {
      throw new Error('邮箱格式不正确');
    }
    return true;
  }),
  body('message').notEmpty().withMessage('留言内容不能为空'),
  body('source').notEmpty().withMessage('来源页面不能为空')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: '数据验证失败',
        details: errors.array()
      });
    }

    const { name, phone, email, company, message, source } = req.body;

    const contact = await prisma.contactForm.create({
      data: {
        name,
        phone,
        email,
        company,
        message,
        source
      }
    });

    res.status(201).json({
      success: true,
      data: contact,
      message: '提交成功，我们会尽快与您联系'
    });
  } catch (error) {
    next(error);
  }
});

export default router;
