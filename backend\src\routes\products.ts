import { Router } from 'express';
import { body, query, validationResult } from 'express-validator';
import prisma from '../lib/prisma';
import { authenticate, authorize } from '../middleware/auth';
import { generateSlug } from '../../../shared/src/utils';

const router = Router();

// 获取产品列表（公开接口）
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
  query('category').optional().isString(),
  query('featured').optional().isBoolean(),
  query('search').optional().isString()
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: '参数验证失败',
        details: errors.array()
      });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const category = req.query.category as string;
    const featured = req.query.featured === 'true';
    const search = req.query.search as string;

    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: any = {
      status: 'PUBLISHED'
    };

    if (category) {
      where.category = {
        slug: category
      };
    }

    if (featured) {
      where.featured = true;
    }

    if (search) {
      where.OR = [
        { name: { contains: search } },
        { description: { contains: search } },
        { shortDescription: { contains: search } }
      ];
    }

    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          }
        },
        orderBy: [
          { featured: 'desc' },
          { sortOrder: 'asc' },
          { createdAt: 'desc' }
        ],
        skip,
        take: limit
      }),
      prisma.product.count({ where })
    ]);

    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: products,
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    });
  } catch (error) {
    next(error);
  }
});

// 获取单个产品详情（公开接口）
router.get('/:slug', async (req, res, next) => {
  try {
    const { slug } = req.params;

    const product = await prisma.product.findUnique({
      where: { 
        slug,
        status: 'PUBLISHED'
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            slug: true
          }
        }
      }
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        error: '产品不存在'
      });
    }

    res.json({
      success: true,
      data: product
    });
  } catch (error) {
    next(error);
  }
});

// 获取产品分类列表（公开接口）
router.get('/categories/list', async (req, res, next) => {
  try {
    const categories = await prisma.productCategory.findMany({
      orderBy: { sortOrder: 'asc' },
      include: {
        _count: {
          select: {
            products: {
              where: {
                status: 'PUBLISHED'
              }
            }
          }
        }
      }
    });

    res.json({
      success: true,
      data: categories
    });
  } catch (error) {
    next(error);
  }
});

// 创建产品（需要管理员权限）
router.post('/',
  authenticate,
  authorize(['SUPER_ADMIN', 'ADMIN', 'EDITOR']),
  [
    body('name').notEmpty().withMessage('产品名称不能为空'),
    body('description').notEmpty().withMessage('产品描述不能为空'),
    body('categoryId').notEmpty().withMessage('产品分类不能为空'),
    body('features').isArray().withMessage('产品特点必须是数组'),
    body('specifications').isObject().withMessage('产品规格必须是对象')
  ],
  async (req, res, next) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          error: '数据验证失败',
          details: errors.array()
        });
      }

      const {
        name,
        description,
        shortDescription,
        features,
        specifications,
        mainImage,
        images,
        categoryId,
        tags,
        status,
        featured,
        sortOrder,
        seoTitle,
        seoDescription,
        seoKeywords
      } = req.body;

      // 生成slug
      const slug = generateSlug(name);

      // 检查slug是否已存在
      const existingProduct = await prisma.product.findUnique({
        where: { slug }
      });

      if (existingProduct) {
        return res.status(400).json({
          success: false,
          error: '产品名称已存在，请使用不同的名称'
        });
      }

      const product = await prisma.product.create({
        data: {
          name,
          slug,
          description,
          shortDescription,
          features,
          specifications,
          mainImage,
          images: images || [],
          categoryId,
          tags: tags || [],
          status: status || 'DRAFT',
          featured: featured || false,
          sortOrder: sortOrder || 0,
          seoTitle,
          seoDescription,
          seoKeywords
        },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          }
        }
      });

      res.status(201).json({
        success: true,
        data: product,
        message: '产品创建成功'
      });
    } catch (error) {
      next(error);
    }
  }
);

export default router;
