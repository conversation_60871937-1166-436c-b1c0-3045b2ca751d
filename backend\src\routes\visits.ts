import { Router } from 'express';
import { body, validationResult } from 'express-validator';
import prisma from '../lib/prisma';
import { getDeviceType, getBrowserInfo, getOSInfo } from '../../../shared/src/utils';

const router = Router();

// 记录访问数据（公开接口）
router.post('/track', [
  body('url').notEmpty().withMessage('访问URL不能为空'),
  body('pageTitle').notEmpty().withMessage('页面标题不能为空')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: '数据验证失败',
        details: errors.array()
      });
    }

    const {
      sessionId,
      url,
      pageTitle,
      referer,
      userAgent,
      deviceType,
      browser,
      os
    } = req.body;

    // 获取客户端IP
    const ipAddress = req.ip || 
                     req.connection.remoteAddress || 
                     req.socket.remoteAddress ||
                     (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
                     'unknown';

    // 如果没有传递设备信息，从User-Agent解析
    const finalUserAgent = userAgent || req.get('User-Agent') || '';
    const finalDeviceType = deviceType || getDeviceType(finalUserAgent);
    const finalBrowser = browser || getBrowserInfo(finalUserAgent);
    const finalOS = os || getOSInfo(finalUserAgent);

    const visit = await prisma.userVisit.create({
      data: {
        sessionId: sessionId || 'unknown',
        ipAddress,
        userAgent: finalUserAgent,
        referer,
        url,
        pageTitle,
        deviceType: finalDeviceType,
        browser: finalBrowser,
        os: finalOS
      }
    });

    res.json({
      success: true,
      data: { id: visit.id }
    });
  } catch (error) {
    next(error);
  }
});

// 更新访问时长
router.put('/duration/:id', [
  body('stayDuration').isInt({ min: 0 }).withMessage('停留时长必须是非负整数')
], async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: '数据验证失败',
        details: errors.array()
      });
    }

    const { id } = req.params;
    const { stayDuration } = req.body;

    await prisma.userVisit.update({
      where: { id },
      data: { stayDuration }
    });

    res.json({
      success: true,
      message: '访问时长更新成功'
    });
  } catch (error) {
    next(error);
  }
});

export default router;
