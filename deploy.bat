@echo off
chcp 65001 >nul
echo 🚀 开始部署POS企业网站...

:: 检查Node.js版本
echo 📋 检查Node.js版本...
node -v
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Node.js，请先安装Node.js 16.x或更高版本
    pause
    exit /b 1
)

:: 安装根目录依赖
echo 📦 安装根目录依赖...
npm install
if %errorlevel% neq 0 (
    echo ❌ 根目录依赖安装失败
    pause
    exit /b 1
)

:: 安装共享模块依赖
echo 📦 安装共享模块依赖...
cd shared
npm install
if %errorlevel% neq 0 (
    echo ❌ 共享模块依赖安装失败
    pause
    exit /b 1
)

:: 构建共享模块
echo 🔨 构建共享模块...
npm run build
if %errorlevel% neq 0 (
    echo ❌ 共享模块构建失败
    pause
    exit /b 1
)
cd ..

:: 安装前端依赖
echo 📦 安装前端依赖...
cd frontend
npm install
if %errorlevel% neq 0 (
    echo ❌ 前端依赖安装失败
    pause
    exit /b 1
)
cd ..

:: 安装后端依赖
echo 📦 安装后端依赖...
cd backend
npm install
if %errorlevel% neq 0 (
    echo ❌ 后端依赖安装失败
    pause
    exit /b 1
)
cd ..

:: 安装管理后台依赖
echo 📦 安装管理后台依赖...
cd admin
npm install
if %errorlevel% neq 0 (
    echo ❌ 管理后台依赖安装失败
    pause
    exit /b 1
)
cd ..

:: 检查环境变量文件
echo 🔧 检查环境配置...
if not exist "backend\.env" (
    echo ⚠️  警告: backend\.env 文件不存在，正在创建...
    copy "backend\.env.example" "backend\.env"
    echo 已创建 backend\.env 文件，请编辑配置后继续
    echo 按任意键打开配置文件...
    pause >nul
    notepad "backend\.env"
    echo 配置完成后按任意键继续...
    pause >nul
)

:: 构建前端项目
echo 🔨 构建前端项目...
cd frontend
npm run build
if %errorlevel% neq 0 (
    echo ❌ 前端构建失败
    pause
    exit /b 1
)
cd ..

:: 构建后端项目
echo 🔨 构建后端项目...
cd backend
npm run build
if %errorlevel% neq 0 (
    echo ❌ 后端构建失败
    pause
    exit /b 1
)
cd ..

:: 构建管理后台
echo 🔨 构建管理后台...
cd admin
npm run build
if %errorlevel% neq 0 (
    echo ❌ 管理后台构建失败
    pause
    exit /b 1
)
cd ..

:: 创建PM2配置文件
echo ⚙️  创建PM2配置...
(
echo module.exports = {
echo   apps: [
echo     {
echo       name: 'pos-backend',
echo       script: './backend/dist/index.js',
echo       instances: 1,
echo       exec_mode: 'cluster',
echo       env: {
echo         NODE_ENV: 'production',
echo         PORT: 3001
echo       },
echo       error_file: './logs/backend-error.log',
echo       out_file: './logs/backend-out.log',
echo       log_file: './logs/backend-combined.log',
echo       time: true
echo     }
echo   ]
echo };
) > ecosystem.config.js

:: 创建日志目录
if not exist "logs" mkdir logs

echo ✅ 构建完成！
echo.
echo 📋 后续步骤:
echo 1. 配置 backend\.env 文件中的数据库连接
echo 2. 运行数据库迁移: cd backend ^&^& npm run db:push ^&^& npm run db:init
echo 3. 启动后端服务: pm2 start ecosystem.config.js
echo 4. 配置Nginx
echo 5. 访问网站测试
echo.
echo 🔧 数据库初始化命令:
echo cd backend
echo npm run db:generate
echo npm run db:push
echo npm run db:init
echo.
echo 🚀 启动服务命令:
echo pm2 start ecosystem.config.js
echo pm2 startup
echo pm2 save
echo.
pause
