#!/bin/bash

# POS企业网站部署脚本
# 适用于宝塔面板LNMP环境

set -e

echo "🚀 开始部署POS企业网站..."

# 检查Node.js版本
echo "📋 检查Node.js版本..."
node_version=$(node -v)
echo "当前Node.js版本: $node_version"

if [[ ! "$node_version" =~ ^v1[6-9]\. ]] && [[ ! "$node_version" =~ ^v[2-9][0-9]\. ]]; then
    echo "❌ 错误: 需要Node.js 16.x或更高版本"
    exit 1
fi

# 安装依赖
echo "📦 安装项目依赖..."
npm install

echo "📦 安装前端依赖..."
cd frontend && npm install && cd ..

echo "📦 安装后端依赖..."
cd backend && npm install && cd ..

echo "📦 安装管理后台依赖..."
cd admin && npm install && cd ..

echo "📦 安装共享模块依赖..."
cd shared && npm install && cd ..

# 构建共享模块
echo "🔨 构建共享模块..."
cd shared && npm run build && cd ..

# 检查环境变量
echo "🔧 检查环境配置..."
if [ ! -f "backend/.env" ]; then
    echo "⚠️  警告: backend/.env 文件不存在，请复制 .env.example 并配置"
    cp backend/.env.example backend/.env
    echo "已创建 backend/.env 文件，请编辑配置后重新运行部署"
    exit 1
fi

# 数据库设置
echo "🗄️  设置数据库..."
cd backend
npm run db:generate
echo "请确保数据库连接正确，然后运行: npm run db:push"
cd ..

# 构建项目
echo "🔨 构建前端项目..."
cd frontend && npm run build && cd ..

echo "🔨 构建后端项目..."
cd backend && npm run build && cd ..

echo "🔨 构建管理后台..."
cd admin && npm run build && cd ..

# 创建PM2配置文件
echo "⚙️  创建PM2配置..."
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: 'pos-backend',
      script: './backend/dist/index.js',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      error_file: './logs/backend-error.log',
      out_file: './logs/backend-out.log',
      log_file: './logs/backend-combined.log',
      time: true
    }
  ]
};
EOF

# 创建日志目录
mkdir -p logs

# 创建Nginx配置示例
echo "🌐 创建Nginx配置示例..."
cat > nginx.conf.example << EOF
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root $(pwd)/frontend/.output/public;
        try_files \$uri \$uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # 后端API
    location /api {
        proxy_pass http://localhost:3001;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
    
    # 管理后台
    location /admin {
        alias $(pwd)/admin/dist;
        try_files \$uri \$uri/ /index.html;
    }
    
    # 文件上传
    location /uploads {
        alias $(pwd)/backend/uploads;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
EOF

echo "✅ 部署完成！"
echo ""
echo "📋 后续步骤:"
echo "1. 配置 backend/.env 文件中的数据库连接"
echo "2. 运行数据库迁移: cd backend && npm run db:push"
echo "3. 启动后端服务: pm2 start ecosystem.config.js"
echo "4. 配置Nginx (参考 nginx.conf.example)"
echo "5. 访问网站: http://your-domain.com"
echo "6. 访问管理后台: http://your-domain.com/admin"
echo ""
echo "🔧 常用命令:"
echo "- 查看PM2状态: pm2 status"
echo "- 重启服务: pm2 restart pos-backend"
echo "- 查看日志: pm2 logs pos-backend"
echo "- 停止服务: pm2 stop pos-backend"
