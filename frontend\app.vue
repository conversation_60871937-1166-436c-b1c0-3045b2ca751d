<template>
  <div>
    <!-- 页面布局 -->
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
    
    <!-- 全局组件 -->
    <ClientOnly>
      <Teleport to="body">
        <!-- Toast通知 -->
        <div id="toast-container"></div>
        
        <!-- 回到顶部按钮 -->
        <BackToTop />
        
        <!-- 客服浮窗 -->
        <CustomerService />
      </Teleport>
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
// 全局SEO配置
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  meta: [
    { name: 'format-detection', content: 'telephone=no' },
    { name: 'msapplication-TileColor', content: '#3b82f6' },
    { name: 'theme-color', content: '#3b82f6' }
  ]
})

// 全局错误处理
onErrorCaptured((error) => {
  console.error('Global error:', error)
  return false
})

// 页面访问统计
const route = useRoute()
const { trackPageView } = useAnalytics()

watch(() => route.fullPath, (newPath) => {
  nextTick(() => {
    trackPageView(newPath)
  })
}, { immediate: true })
</script>

<style>
/* 全局样式重置 */
* {
  box-sizing: border-box;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 选择文本样式 */
::selection {
  background-color: #3b82f6;
  color: white;
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
</style>
