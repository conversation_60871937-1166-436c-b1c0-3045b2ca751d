<template>
  <section class="section-padding bg-gray-50">
    <div class="container-custom">
      <div class="text-center mb-16">
        <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
          联系我们
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          有任何问题或需求，请随时与我们联系。我们的专业团队将为您提供最优质的服务。
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        <!-- 联系表单 -->
        <div class="bg-white rounded-2xl shadow-lg p-8">
          <h3 class="text-2xl font-bold text-gray-900 mb-6">发送消息</h3>
          
          <form @submit.prevent="submitForm" class="space-y-6">
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                  姓名 <span class="text-red-500">*</span>
                </label>
                <input
                  id="name"
                  v-model="form.name"
                  type="text"
                  required
                  class="form-input"
                  placeholder="请输入您的姓名"
                >
              </div>
              <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                  手机号 <span class="text-red-500">*</span>
                </label>
                <input
                  id="phone"
                  v-model="form.phone"
                  type="tel"
                  required
                  class="form-input"
                  placeholder="请输入您的手机号"
                >
              </div>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                  邮箱
                </label>
                <input
                  id="email"
                  v-model="form.email"
                  type="email"
                  class="form-input"
                  placeholder="请输入您的邮箱"
                >
              </div>
              <div>
                <label for="company" class="block text-sm font-medium text-gray-700 mb-2">
                  公司名称
                </label>
                <input
                  id="company"
                  v-model="form.company"
                  type="text"
                  class="form-input"
                  placeholder="请输入您的公司名称"
                >
              </div>
            </div>

            <div>
              <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                留言内容 <span class="text-red-500">*</span>
              </label>
              <textarea
                id="message"
                v-model="form.message"
                rows="4"
                required
                class="form-textarea"
                placeholder="请详细描述您的需求..."
              ></textarea>
            </div>

            <button
              type="submit"
              :disabled="isSubmitting"
              class="w-full btn btn-primary btn-lg"
              :class="{ 'opacity-50 cursor-not-allowed': isSubmitting }"
            >
              <span v-if="isSubmitting">提交中...</span>
              <span v-else>发送消息</span>
            </button>
          </form>
        </div>

        <!-- 联系信息 -->
        <div class="space-y-8">
          <!-- 公司信息卡片 -->
          <div class="bg-white rounded-2xl shadow-lg p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-6">联系信息</h3>
            
            <div class="space-y-6">
              <div v-if="companyInfo?.address" class="flex items-start">
                <div class="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-4">
                  <h4 class="text-lg font-semibold text-gray-900">地址</h4>
                  <p class="text-gray-600">{{ companyInfo.address }}</p>
                </div>
              </div>

              <div v-if="companyInfo?.phone" class="flex items-start">
                <div class="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                  </svg>
                </div>
                <div class="ml-4">
                  <h4 class="text-lg font-semibold text-gray-900">电话</h4>
                  <p class="text-gray-600">{{ companyInfo.phone }}</p>
                </div>
              </div>

              <div v-if="companyInfo?.email" class="flex items-start">
                <div class="flex-shrink-0 w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                  <svg class="w-6 h-6 text-primary-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                    <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                  </svg>
                </div>
                <div class="ml-4">
                  <h4 class="text-lg font-semibold text-gray-900">邮箱</h4>
                  <p class="text-gray-600">{{ companyInfo.email }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 工作时间 -->
          <div class="bg-primary-600 rounded-2xl shadow-lg p-8 text-white">
            <h3 class="text-2xl font-bold mb-6">工作时间</h3>
            <div class="space-y-3">
              <div class="flex justify-between">
                <span>周一 - 周五</span>
                <span>9:00 - 18:00</span>
              </div>
              <div class="flex justify-between">
                <span>周六</span>
                <span>9:00 - 17:00</span>
              </div>
              <div class="flex justify-between">
                <span>周日</span>
                <span>休息</span>
              </div>
            </div>
            <div class="mt-6 pt-6 border-t border-primary-500">
              <p class="text-primary-100">
                紧急情况请拨打24小时服务热线
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
const { getCompanyInfo, submitContact } = useApi()
const { trackFormSubmit } = useAnalytics()

// 获取公司信息
const { data: companyResponse } = await getCompanyInfo()
const companyInfo = companyResponse?.data

// 表单数据
const form = reactive({
  name: '',
  phone: '',
  email: '',
  company: '',
  message: ''
})

// 提交状态
const isSubmitting = ref(false)

// 提交表单
const submitForm = async () => {
  if (isSubmitting.value) return

  isSubmitting.value = true

  try {
    const response = await submitContact({
      ...form,
      source: 'homepage_contact_section'
    })

    if (response.success) {
      // 统计表单提交
      await trackFormSubmit('contact_form', {
        source: 'homepage_contact_section',
        has_company: !!form.company,
        has_email: !!form.email
      })

      // 显示成功消息
      alert('提交成功！我们会尽快与您联系。')
      
      // 重置表单
      Object.keys(form).forEach(key => {
        form[key as keyof typeof form] = ''
      })
    } else {
      alert(response.error || '提交失败，请稍后重试')
    }
  } catch (error) {
    console.error('表单提交失败:', error)
    alert('提交失败，请稍后重试')
  } finally {
    isSubmitting.value = false
  }
}
</script>
