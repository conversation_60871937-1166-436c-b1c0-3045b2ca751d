<template>
  <section class="relative bg-gradient-to-br from-primary-600 to-primary-800 overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0">
      <div class="absolute inset-0 bg-black opacity-20"></div>
      <div class="absolute inset-0 bg-gradient-to-r from-primary-600/90 to-transparent"></div>
    </div>

    <!-- 内容 -->
    <div class="relative container-custom">
      <div class="py-24 lg:py-32">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <!-- 左侧内容 -->
          <div class="text-white">
            <h1 class="text-4xl lg:text-6xl font-bold leading-tight mb-6">
              专业的
              <span class="text-gradient bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent">
                POS解决方案
              </span>
              提供商
            </h1>
            <p class="text-xl lg:text-2xl text-blue-100 mb-8 leading-relaxed">
              {{ companyInfo?.description || '致力于为企业提供高质量的收银设备、支付终端和完善的售后服务，助力企业数字化转型' }}
            </p>
            
            <!-- CTA按钮 -->
            <div class="flex flex-col sm:flex-row gap-4">
              <NuxtLink
                to="/products"
                class="btn btn-lg bg-white text-primary-600 hover:bg-gray-50 font-semibold px-8 py-4 rounded-lg shadow-lg transform hover:scale-105 transition-all duration-200"
              >
                查看产品
              </NuxtLink>
              <NuxtLink
                to="/contact"
                class="btn btn-lg border-2 border-white text-white hover:bg-white hover:text-primary-600 font-semibold px-8 py-4 rounded-lg transition-all duration-200"
              >
                联系我们
              </NuxtLink>
            </div>

            <!-- 特色数据 -->
            <div class="grid grid-cols-3 gap-8 mt-12 pt-8 border-t border-blue-400/30">
              <div class="text-center">
                <div class="text-3xl font-bold text-yellow-400 mb-2">{{ stats.experience }}+</div>
                <div class="text-blue-100">年行业经验</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold text-yellow-400 mb-2">{{ stats.customers }}+</div>
                <div class="text-blue-100">服务客户</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold text-yellow-400 mb-2">{{ stats.products }}+</div>
                <div class="text-blue-100">产品型号</div>
              </div>
            </div>
          </div>

          <!-- 右侧图片/视频 -->
          <div class="relative">
            <div class="relative z-10">
              <img
                src="/images/hero-pos-device.jpg"
                alt="POS设备展示"
                class="w-full h-auto rounded-2xl shadow-2xl transform hover:scale-105 transition-transform duration-300"
                loading="eager"
              >
              
              <!-- 浮动卡片 -->
              <div class="absolute -bottom-6 -left-6 bg-white rounded-xl p-6 shadow-xl max-w-xs">
                <div class="flex items-center mb-3">
                  <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                  <span class="text-sm font-medium text-gray-600">实时在线</span>
                </div>
                <div class="text-2xl font-bold text-gray-900 mb-1">99.9%</div>
                <div class="text-sm text-gray-600">系统稳定性</div>
              </div>
            </div>

            <!-- 背景装饰元素 -->
            <div class="absolute top-4 right-4 w-72 h-72 bg-yellow-400/20 rounded-full blur-3xl"></div>
            <div class="absolute -bottom-8 -right-8 w-64 h-64 bg-orange-400/20 rounded-full blur-3xl"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 滚动指示器 -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2">
      <div class="animate-bounce">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
        </svg>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
const { getCompanyInfo } = useApi()

// 获取公司信息
const { data: companyResponse } = await getCompanyInfo()
const companyInfo = companyResponse?.data

// 统计数据
const stats = {
  experience: companyInfo?.foundedYear ? new Date().getFullYear() - companyInfo.foundedYear : 10,
  customers: 500,
  products: 50
}
</script>

<style scoped>
.text-gradient {
  background: linear-gradient(135deg, #fbbf24 0%, #f97316 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
</style>
