export const useAnalytics = () => {
  const { trackVisit } = useApi()
  
  // 生成会话ID
  const getSessionId = () => {
    let sessionId = sessionStorage.getItem('sessionId')
    if (!sessionId) {
      sessionId = Math.random().toString(36).substr(2, 9) + Date.now().toString(36)
      sessionStorage.setItem('sessionId', sessionId)
    }
    return sessionId
  }

  // 获取设备信息
  const getDeviceInfo = () => {
    const userAgent = navigator.userAgent
    
    // 检测设备类型
    let deviceType: 'desktop' | 'tablet' | 'mobile' = 'desktop'
    if (/(tablet|ipad|playbook|silk)|(android(?!.*mobi))/i.test(userAgent)) {
      deviceType = 'tablet'
    } else if (/Mobile|iP(hone|od)|Android|BlackBerry|IEMobile|Kindle|Silk-Accelerated|(hpw|web)OS|Opera M(obi|ini)/.test(userAgent)) {
      deviceType = 'mobile'
    }

    // 检测浏览器
    let browser = 'Unknown'
    if (userAgent.includes('Chrome')) browser = 'Chrome'
    else if (userAgent.includes('Firefox')) browser = 'Firefox'
    else if (userAgent.includes('Safari')) browser = 'Safari'
    else if (userAgent.includes('Edge')) browser = 'Edge'
    else if (userAgent.includes('Opera')) browser = 'Opera'

    // 检测操作系统
    let os = 'Unknown'
    if (userAgent.includes('Windows')) os = 'Windows'
    else if (userAgent.includes('Mac')) os = 'macOS'
    else if (userAgent.includes('Linux')) os = 'Linux'
    else if (userAgent.includes('Android')) os = 'Android'
    else if (userAgent.includes('iOS')) os = 'iOS'

    return { deviceType, browser, os }
  }

  // 页面访问统计
  const trackPageView = async (path: string) => {
    if (process.client) {
      try {
        const { deviceType, browser, os } = getDeviceInfo()
        
        await trackVisit({
          sessionId: getSessionId(),
          url: path,
          pageTitle: document.title,
          referer: document.referrer || undefined,
          userAgent: navigator.userAgent,
          deviceType,
          browser,
          os
        })
      } catch (error) {
        console.error('页面访问统计失败:', error)
      }
    }
  }

  // 事件统计
  const trackEvent = async (eventName: string, eventData?: Record<string, any>) => {
    if (process.client) {
      try {
        // 这里可以扩展自定义事件统计
        console.log('事件统计:', eventName, eventData)
      } catch (error) {
        console.error('事件统计失败:', error)
      }
    }
  }

  // 表单提交统计
  const trackFormSubmit = async (formName: string, formData?: Record<string, any>) => {
    await trackEvent('form_submit', {
      form_name: formName,
      ...formData
    })
  }

  // 产品查看统计
  const trackProductView = async (productId: string, productName: string) => {
    await trackEvent('product_view', {
      product_id: productId,
      product_name: productName
    })
  }

  return {
    trackPageView,
    trackEvent,
    trackFormSubmit,
    trackProductView
  }
}
