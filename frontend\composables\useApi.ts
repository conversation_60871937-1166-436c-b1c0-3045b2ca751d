import type { ApiResponse, PaginatedResponse } from '@pos-website/shared'

interface FetchOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  body?: any
  headers?: Record<string, string>
  query?: Record<string, any>
}

export const useApi = () => {
  const config = useRuntimeConfig()
  const baseURL = config.public.apiBase

  const apiFetch = async <T = any>(
    endpoint: string, 
    options: FetchOptions = {}
  ): Promise<ApiResponse<T>> => {
    const { method = 'GET', body, headers = {}, query } = options

    // 构建URL
    let url = `${baseURL}${endpoint}`
    if (query) {
      const searchParams = new URLSearchParams()
      Object.entries(query).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value))
        }
      })
      if (searchParams.toString()) {
        url += `?${searchParams.toString()}`
      }
    }

    // 设置请求头
    const requestHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      ...headers
    }

    try {
      const response = await $fetch<ApiResponse<T>>(url, {
        method,
        headers: requestHeaders,
        body: body ? JSON.stringify(body) : undefined
      })

      return response
    } catch (error: any) {
      console.error('API请求失败:', error)
      
      // 处理错误响应
      if (error.data) {
        return error.data
      }
      
      return {
        success: false,
        error: error.message || '网络请求失败'
      }
    }
  }

  return {
    // 公司信息
    getCompanyInfo: () => apiFetch('/company'),
    
    // 产品相关
    getProducts: (params?: {
      page?: number
      limit?: number
      category?: string
      featured?: boolean
      search?: string
    }) => apiFetch<PaginatedResponse<any>>('/products', { query: params }),
    
    getProduct: (slug: string) => apiFetch(`/products/${slug}`),
    
    getProductCategories: () => apiFetch('/products/categories/list'),
    
    // 文章相关
    getArticles: (params?: {
      page?: number
      limit?: number
      category?: string
      featured?: boolean
    }) => apiFetch<PaginatedResponse<any>>('/articles', { query: params }),
    
    getArticle: (slug: string) => apiFetch(`/articles/${slug}`),
    
    getArticleCategories: () => apiFetch('/articles/categories/list'),
    
    // 联系表单
    submitContact: (data: {
      name: string
      phone: string
      email?: string
      company?: string
      message: string
      source: string
    }) => apiFetch('/contact', { method: 'POST', body: data }),
    
    // 访问统计
    trackVisit: (data: {
      url: string
      pageTitle: string
      referer?: string
    }) => apiFetch('/visits/track', { method: 'POST', body: data }),
    
    // SEO设置
    getSeoSettings: (page: string) => apiFetch(`/seo/${page}`)
  }
}
