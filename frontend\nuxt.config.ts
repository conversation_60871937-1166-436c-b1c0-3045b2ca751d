// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },
  
  modules: [
    '@nuxtjs/tailwindcss',
    '@pinia/nuxt',
    '@vueuse/nuxt',
    '@nuxtjs/google-fonts'
  ],

  // CSS配置
  css: ['~/assets/css/main.css'],

  // Google Fonts配置
  googleFonts: {
    families: {
      'Noto Sans SC': [300, 400, 500, 600, 700],
      'Inter': [300, 400, 500, 600, 700]
    },
    display: 'swap'
  },

  // 运行时配置
  runtimeConfig: {
    // 私有配置（仅在服务端可用）
    jwtSecret: process.env.JWT_SECRET,
    
    // 公共配置（客户端和服务端都可用）
    public: {
      apiBase: process.env.API_BASE_URL || 'http://localhost:3001/api',
      siteUrl: process.env.SITE_URL || 'http://localhost:3000',
      siteName: 'POS企业官网',
      siteDescription: '专业的POS产品解决方案提供商'
    }
  },

  // SEO配置
  app: {
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
      title: 'POS企业官网 - 专业的POS产品解决方案',
      meta: [
        { name: 'description', content: '专业的POS产品解决方案提供商，提供高质量的POS设备和完善的售后服务' },
        { name: 'keywords', content: 'POS,收银机,支付终端,企业解决方案' },
        { name: 'author', content: 'POS企业' },
        { property: 'og:type', content: 'website' },
        { property: 'og:site_name', content: 'POS企业官网' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }
      ]
    }
  },

  // 构建配置
  nitro: {
    compressPublicAssets: true
  },

  // TypeScript配置
  typescript: {
    strict: true
  }
})
