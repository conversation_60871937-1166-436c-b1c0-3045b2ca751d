# POS企业网站 - Web安装向导

## 🚀 快速安装

### 1. 上传文件
将整个项目文件夹上传到您的宝塔面板网站目录：
```
/www/wwwroot/your-domain.com/
```

### 2. 访问安装向导
在浏览器中访问：
```
http://your-domain.com/install/
```

### 3. 按照向导完成安装
安装向导将引导您完成以下步骤：
1. **环境检查** - 检查服务器环境是否满足要求
2. **数据库配置** - 配置MySQL数据库连接
3. **网站配置** - 设置网站基本信息
4. **管理员账号** - 创建管理后台账号
5. **安装完成** - 自动完成安装并启动服务

## 📋 环境要求

### 服务器环境
- **Node.js**: 16.x 或更高版本
- **NPM**: 6.x 或更高版本
- **MySQL**: 5.7 或更高版本
- **PHP**: 7.4 或更高版本（用于安装向导）

### 宝塔面板设置
1. 确保已安装Node.js管理器
2. 确保已安装MySQL数据库
3. 确保网站目录有写入权限

## 🔧 手动安装（备选方案）

如果Web安装向导无法正常工作，您可以使用手动安装：

### Windows环境
```batch
# 运行Windows批处理脚本
deploy.bat
```

### Linux环境
```bash
# 运行Linux部署脚本
chmod +x deploy.sh
./deploy.sh
```

## 📁 安装后的目录结构

```
/www/wwwroot/your-domain.com/
├── backend/          # 后端API服务
├── frontend/         # 前端网站
├── admin/           # 管理后台
├── shared/          # 共享模块
├── install/         # 安装向导（可删除）
├── logs/            # 日志文件
├── ecosystem.config.js  # PM2配置
└── install.lock     # 安装锁文件
```

## 🌐 访问地址

安装完成后，您可以通过以下地址访问：

- **企业官网**: http://your-domain.com
- **管理后台**: http://your-domain.com/admin

## 🔐 默认管理员账号

管理员账号信息在安装过程中由您自定义设置。

## ⚙️ 服务管理

### 查看服务状态
```bash
pm2 status
```

### 重启服务
```bash
pm2 restart pos-backend
```

### 查看日志
```bash
pm2 logs pos-backend
```

### 停止服务
```bash
pm2 stop pos-backend
```

## 🔧 Nginx配置

安装完成后，请在宝塔面板中配置Nginx：

```nginx
# 前端静态文件
location / {
    root /www/wwwroot/your-domain.com/frontend/.output/public;
    try_files $uri $uri/ /index.html;
}

# 后端API
location /api {
    proxy_pass http://localhost:3001;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

# 管理后台
location /admin {
    alias /www/wwwroot/your-domain.com/admin/dist;
    try_files $uri $uri/ /index.html;
}

# 文件上传
location /uploads {
    alias /www/wwwroot/your-domain.com/backend/uploads;
    expires 1y;
    add_header Cache-Control "public";
}
```

## 🛡️ 安全建议

1. **删除安装目录**: 安装完成后删除 `install/` 目录
2. **修改默认密码**: 首次登录后立即修改管理员密码
3. **启用HTTPS**: 配置SSL证书启用HTTPS
4. **定期备份**: 设置定期数据库备份
5. **更新系统**: 定期更新服务器和软件包

## 🔍 故障排除

### 常见问题

#### 1. 环境检查失败
- 确保Node.js版本 >= 16.x
- 确保NPM已正确安装
- 检查目录写入权限

#### 2. 数据库连接失败
- 检查MySQL服务是否启动
- 验证数据库用户名和密码
- 确认数据库用户有创建数据库的权限

#### 3. 安装过程中断
- 检查服务器磁盘空间
- 查看PHP错误日志
- 确保网络连接稳定

#### 4. 服务启动失败
- 检查端口3001是否被占用
- 查看PM2日志: `pm2 logs pos-backend`
- 检查环境变量配置

### 获取帮助

如果遇到问题：
1. 查看浏览器控制台错误信息
2. 检查服务器错误日志
3. 参考详细部署文档
4. 联系技术支持

## 📞 技术支持

- 项目文档: 查看根目录的README.md
- 部署指南: 查看DEPLOYMENT.md
- 快速开始: 查看QUICKSTART.md

---

**注意**: 安装完成后，建议删除 `install/` 目录以提高安全性。
