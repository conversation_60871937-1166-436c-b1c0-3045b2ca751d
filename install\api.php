<?php
/**
 * POS企业网站安装API
 * 处理Web安装向导的后端逻辑
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 获取请求参数
$action = $_GET['action'] ?? $_POST['action'] ?? '';
$data = json_decode(file_get_contents('php://input'), true) ?? $_POST;

// 项目根目录
$projectRoot = dirname(__DIR__);

try {
    switch ($action) {
        case 'check_environment':
            echo json_encode(checkEnvironment());
            break;
            
        case 'test_database':
            echo json_encode(testDatabase($data));
            break;
            
        case 'install':
            echo json_encode(performInstall($data));
            break;
            
        default:
            throw new Exception('未知的操作');
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * 检查环境要求
 */
function checkEnvironment() {
    global $projectRoot;
    
    $checks = [
        'node' => false,
        'npm' => false,
        'write_permission' => false,
        'port_3001' => false
    ];
    
    // 检查Node.js
    $nodeVersion = shell_exec('node -v 2>&1');
    if ($nodeVersion && preg_match('/v(\d+)\./', $nodeVersion, $matches)) {
        $checks['node'] = intval($matches[1]) >= 16;
    }
    
    // 检查NPM
    $npmVersion = shell_exec('npm -v 2>&1');
    if ($npmVersion && preg_match('/(\d+)\./', $npmVersion, $matches)) {
        $checks['npm'] = intval($matches[1]) >= 6;
    }
    
    // 检查写入权限
    $checks['write_permission'] = is_writable($projectRoot);
    
    // 检查端口3001
    $portCheck = shell_exec('netstat -tlnp 2>/dev/null | grep :3001');
    $checks['port_3001'] = empty($portCheck);
    
    return [
        'success' => true,
        'data' => $checks
    ];
}

/**
 * 测试数据库连接
 */
function testDatabase($config) {
    $host = $config['host'] ?? 'localhost';
    $port = $config['port'] ?? 3306;
    $database = $config['database'] ?? '';
    $username = $config['username'] ?? '';
    $password = $config['password'] ?? '';
    
    try {
        $dsn = "mysql:host={$host};port={$port};charset=utf8mb4";
        $pdo = new PDO($dsn, $username, $password, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_TIMEOUT => 5
        ]);
        
        // 检查数据库是否存在，不存在则创建
        $stmt = $pdo->prepare("CREATE DATABASE IF NOT EXISTS `{$database}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $stmt->execute();
        
        return [
            'success' => true,
            'message' => '数据库连接成功'
        ];
        
    } catch (PDOException $e) {
        return [
            'success' => false,
            'error' => '数据库连接失败: ' . $e->getMessage()
        ];
    }
}

/**
 * 执行安装
 */
function performInstall($config) {
    global $projectRoot;
    
    try {
        // 1. 创建环境配置文件
        createEnvFile($config);
        
        // 2. 安装依赖
        installDependencies();
        
        // 3. 构建项目
        buildProject();
        
        // 4. 初始化数据库
        initializeDatabase($config);
        
        // 5. 启动服务
        startServices();
        
        // 6. 创建安装锁文件
        createInstallLock();
        
        return [
            'success' => true,
            'message' => '安装完成',
            'data' => [
                'frontend_url' => $config['site']['siteUrl'],
                'admin_url' => $config['site']['siteUrl'] . '/admin'
            ]
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => '安装失败: ' . $e->getMessage()
        ];
    }
}

/**
 * 创建环境配置文件
 */
function createEnvFile($config) {
    global $projectRoot;
    
    $dbConfig = $config['database'];
    $siteConfig = $config['site'];
    
    $envContent = "# 数据库配置\n";
    $envContent .= "DATABASE_URL=\"mysql://{$dbConfig['username']}:{$dbConfig['password']}@{$dbConfig['host']}:{$dbConfig['port']}/{$dbConfig['database']}\"\n\n";
    
    $envContent .= "# JWT密钥\n";
    $envContent .= "JWT_SECRET=\"" . generateRandomString(64) . "\"\n";
    $envContent .= "JWT_EXPIRES_IN=\"7d\"\n\n";
    
    $envContent .= "# 服务器配置\n";
    $envContent .= "PORT=3001\n";
    $envContent .= "NODE_ENV=\"production\"\n\n";
    
    $envContent .= "# 文件上传配置\n";
    $envContent .= "UPLOAD_DIR=\"uploads\"\n";
    $envContent .= "MAX_FILE_SIZE=5242880\n\n";
    
    $envContent .= "# CORS配置\n";
    $envContent .= "FRONTEND_URL=\"{$siteConfig['siteUrl']}\"\n";
    $envContent .= "ADMIN_URL=\"{$siteConfig['siteUrl']}/admin\"\n";
    
    $envFile = $projectRoot . '/backend/.env';
    if (file_put_contents($envFile, $envContent) === false) {
        throw new Exception('无法创建环境配置文件');
    }
}

/**
 * 安装依赖
 */
function installDependencies() {
    global $projectRoot;
    
    $commands = [
        "cd {$projectRoot} && npm install",
        "cd {$projectRoot}/shared && npm install && npm run build",
        "cd {$projectRoot}/backend && npm install",
        "cd {$projectRoot}/frontend && npm install",
        "cd {$projectRoot}/admin && npm install"
    ];
    
    foreach ($commands as $command) {
        $output = shell_exec($command . ' 2>&1');
        if (strpos($output, 'error') !== false || strpos($output, 'Error') !== false) {
            throw new Exception('依赖安装失败: ' . $output);
        }
    }
}

/**
 * 构建项目
 */
function buildProject() {
    global $projectRoot;
    
    $commands = [
        "cd {$projectRoot}/backend && npm run build",
        "cd {$projectRoot}/frontend && npm run build",
        "cd {$projectRoot}/admin && npm run build"
    ];
    
    foreach ($commands as $command) {
        $output = shell_exec($command . ' 2>&1');
        if (strpos($output, 'error') !== false || strpos($output, 'Error') !== false) {
            throw new Exception('项目构建失败: ' . $output);
        }
    }
}

/**
 * 初始化数据库
 */
function initializeDatabase($config) {
    global $projectRoot;
    
    // 生成Prisma客户端
    $output = shell_exec("cd {$projectRoot}/backend && npm run db:generate 2>&1");
    if (strpos($output, 'error') !== false) {
        throw new Exception('Prisma客户端生成失败: ' . $output);
    }
    
    // 推送数据库结构
    $output = shell_exec("cd {$projectRoot}/backend && npm run db:push 2>&1");
    if (strpos($output, 'error') !== false) {
        throw new Exception('数据库结构推送失败: ' . $output);
    }
    
    // 初始化数据（需要修改init-data.ts以支持自定义配置）
    createCustomInitScript($config);
    $output = shell_exec("cd {$projectRoot}/backend && npx ts-node scripts/custom-init.ts 2>&1");
    if (strpos($output, 'error') !== false) {
        throw new Exception('数据初始化失败: ' . $output);
    }
}

/**
 * 创建自定义初始化脚本
 */
function createCustomInitScript($config) {
    global $projectRoot;
    
    $adminConfig = $config['admin'];
    $siteConfig = $config['site'];
    
    $scriptContent = "import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🚀 开始初始化数据库数据...')

  try {
    // 创建管理员用户
    const hashedPassword = await bcrypt.hash('{$adminConfig['password']}', 12)
    
    const adminUser = await prisma.adminUser.upsert({
      where: { username: '{$adminConfig['username']}' },
      update: {},
      create: {
        username: '{$adminConfig['username']}',
        email: '{$adminConfig['email']}',
        password: hashedPassword,
        role: 'SUPER_ADMIN'
      }
    })
    console.log('✅ 创建管理员用户:', adminUser.username)

    // 创建公司信息
    const companyInfo = await prisma.companyInfo.upsert({
      where: { id: 'default' },
      update: {},
      create: {
        id: 'default',
        name: '{$siteConfig['siteName']}',
        description: '{$siteConfig['siteDescription']}',
        foundedYear: " . date('Y') . ",
        address: '{$siteConfig['companyAddress']}',
        phone: '{$siteConfig['companyPhone']}',
        email: '{$siteConfig['companyEmail']}',
        website: '{$siteConfig['siteUrl']}',
        vision: '成为行业领先的解决方案提供商',
        mission: '通过创新技术和优质服务，为客户创造价值',
        values: ['创新', '诚信', '专业', '服务']
      }
    })
    console.log('✅ 创建公司信息:', companyInfo.name)

    console.log('🎉 数据库初始化完成！')
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.\$disconnect()
  })";
    
    $scriptFile = $projectRoot . '/backend/scripts/custom-init.ts';
    if (file_put_contents($scriptFile, $scriptContent) === false) {
        throw new Exception('无法创建初始化脚本');
    }
}

/**
 * 启动服务
 */
function startServices() {
    global $projectRoot;
    
    // 创建PM2配置文件
    $pm2Config = "module.exports = {
  apps: [
    {
      name: 'pos-backend',
      script: './backend/dist/index.js',
      instances: 1,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      error_file: './logs/backend-error.log',
      out_file: './logs/backend-out.log',
      log_file: './logs/backend-combined.log',
      time: true
    }
  ]
};";
    
    file_put_contents($projectRoot . '/ecosystem.config.js', $pm2Config);
    
    // 创建日志目录
    if (!is_dir($projectRoot . '/logs')) {
        mkdir($projectRoot . '/logs', 0755, true);
    }
    
    // 启动PM2服务
    $output = shell_exec("cd {$projectRoot} && pm2 start ecosystem.config.js 2>&1");
    if (strpos($output, 'error') !== false) {
        throw new Exception('服务启动失败: ' . $output);
    }
}

/**
 * 创建安装锁文件
 */
function createInstallLock() {
    global $projectRoot;
    
    $lockContent = json_encode([
        'installed' => true,
        'install_time' => date('Y-m-d H:i:s'),
        'version' => '1.0.0'
    ]);
    
    file_put_contents($projectRoot . '/install.lock', $lockContent);
}

/**
 * 生成随机字符串
 */
function generateRandomString($length = 32) {
    return bin2hex(random_bytes($length / 2));
}
?>"
