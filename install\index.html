<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS企业网站 - 安装向导</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .install-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 800px;
            overflow: hidden;
        }

        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .install-header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }

        .install-header p {
            opacity: 0.9;
            font-size: 16px;
        }

        .install-content {
            padding: 40px;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin-bottom: 40px;
        }

        .step {
            display: flex;
            align-items: center;
            margin: 0 10px;
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e0e0e0;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 8px;
        }

        .step.active .step-number {
            background: #667eea;
            color: white;
        }

        .step.completed .step-number {
            background: #4caf50;
            color: white;
        }

        .step-title {
            font-size: 14px;
            color: #666;
        }

        .step.active .step-title {
            color: #667eea;
            font-weight: bold;
        }

        .step-content {
            display: none;
        }

        .step-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        }

        .form-row {
            display: flex;
            gap: 20px;
        }

        .form-row .form-group {
            flex: 1;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-group {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
        }

        .alert {
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .progress-fill {
            height: 100%;
            background: #667eea;
            transition: width 0.3s;
        }

        .requirement-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .requirement-item:last-child {
            border-bottom: none;
        }

        .requirement-status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
        }

        .requirement-status.success {
            background: #4caf50;
        }

        .requirement-status.error {
            background: #f44336;
        }

        .requirement-status.warning {
            background: #ff9800;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1>POS企业网站</h1>
            <p>欢迎使用安装向导，我们将引导您完成网站的安装配置</p>
        </div>

        <div class="install-content">
            <!-- 进度条 -->
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 20%"></div>
            </div>

            <!-- 步骤指示器 -->
            <div class="step-indicator">
                <div class="step active" data-step="1">
                    <div class="step-number">1</div>
                    <div class="step-title">环境检查</div>
                </div>
                <div class="step" data-step="2">
                    <div class="step-number">2</div>
                    <div class="step-title">数据库配置</div>
                </div>
                <div class="step" data-step="3">
                    <div class="step-number">3</div>
                    <div class="step-title">网站配置</div>
                </div>
                <div class="step" data-step="4">
                    <div class="step-number">4</div>
                    <div class="step-title">管理员账号</div>
                </div>
                <div class="step" data-step="5">
                    <div class="step-number">5</div>
                    <div class="step-title">安装完成</div>
                </div>
            </div>

            <!-- 步骤1: 环境检查 -->
            <div class="step-content active" id="step1">
                <h2>环境检查</h2>
                <p>正在检查您的服务器环境是否满足安装要求...</p>

                <div id="requirements">
                    <div class="requirement-item">
                        <div class="requirement-status" id="nodeStatus">?</div>
                        <div>
                            <strong>Node.js 版本</strong>
                            <div>需要 Node.js 16.0 或更高版本</div>
                        </div>
                    </div>
                    <div class="requirement-item">
                        <div class="requirement-status" id="npmStatus">?</div>
                        <div>
                            <strong>NPM 包管理器</strong>
                            <div>需要 NPM 6.0 或更高版本</div>
                        </div>
                    </div>
                    <div class="requirement-item">
                        <div class="requirement-status" id="writeStatus">?</div>
                        <div>
                            <strong>文件写入权限</strong>
                            <div>需要对网站目录有写入权限</div>
                        </div>
                    </div>
                    <div class="requirement-item">
                        <div class="requirement-status" id="portStatus">?</div>
                        <div>
                            <strong>端口可用性</strong>
                            <div>需要端口 3001 可用</div>
                        </div>
                    </div>
                </div>

                <div class="btn-group">
                    <div></div>
                    <button class="btn btn-primary" onclick="checkEnvironment()">检查环境</button>
                </div>
            </div>

            <!-- 步骤2: 数据库配置 -->
            <div class="step-content" id="step2">
                <h2>数据库配置</h2>
                <p>请输入您的MySQL数据库连接信息</p>

                <form id="dbForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="dbHost">数据库主机</label>
                            <input type="text" id="dbHost" name="dbHost" value="localhost" required>
                        </div>
                        <div class="form-group">
                            <label for="dbPort">端口</label>
                            <input type="number" id="dbPort" name="dbPort" value="3306" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="dbName">数据库名称</label>
                        <input type="text" id="dbName" name="dbName" placeholder="pos_website" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="dbUser">用户名</label>
                            <input type="text" id="dbUser" name="dbUser" required>
                        </div>
                        <div class="form-group">
                            <label for="dbPassword">密码</label>
                            <input type="password" id="dbPassword" name="dbPassword">
                        </div>
                    </div>

                    <button type="button" class="btn btn-secondary" onclick="testDatabase()">测试连接</button>
                </form>

                <div class="btn-group">
                    <button class="btn btn-secondary" onclick="prevStep()">上一步</button>
                    <button class="btn btn-primary" onclick="nextStep()" id="dbNextBtn" disabled>下一步</button>
                </div>
            </div>

            <!-- 步骤3: 网站配置 -->
            <div class="step-content" id="step3">
                <h2>网站配置</h2>
                <p>请配置您的网站基本信息</p>

                <form id="siteForm">
                    <div class="form-group">
                        <label for="siteName">网站名称</label>
                        <input type="text" id="siteName" name="siteName" value="POS企业有限公司" required>
                    </div>

                    <div class="form-group">
                        <label for="siteUrl">网站地址</label>
                        <input type="url" id="siteUrl" name="siteUrl" value="http://*************" required>
                    </div>

                    <div class="form-group">
                        <label for="siteDescription">网站描述</label>
                        <textarea id="siteDescription" name="siteDescription" rows="3" placeholder="专业的POS产品解决方案提供商..."></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="companyPhone">联系电话</label>
                            <input type="tel" id="companyPhone" name="companyPhone" placeholder="************">
                        </div>
                        <div class="form-group">
                            <label for="companyEmail">联系邮箱</label>
                            <input type="email" id="companyEmail" name="companyEmail" placeholder="<EMAIL>">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="companyAddress">公司地址</label>
                        <input type="text" id="companyAddress" name="companyAddress" placeholder="请输入公司地址">
                    </div>
                </form>

                <div class="btn-group">
                    <button class="btn btn-secondary" onclick="prevStep()">上一步</button>
                    <button class="btn btn-primary" onclick="nextStep()">下一步</button>
                </div>
            </div>

            <!-- 步骤4: 管理员账号 -->
            <div class="step-content" id="step4">
                <h2>创建管理员账号</h2>
                <p>请设置管理后台的超级管理员账号</p>

                <form id="adminForm">
                    <div class="form-group">
                        <label for="adminUsername">用户名</label>
                        <input type="text" id="adminUsername" name="adminUsername" value="admin" required>
                    </div>

                    <div class="form-group">
                        <label for="adminEmail">邮箱</label>
                        <input type="email" id="adminEmail" name="adminEmail" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="adminPassword">密码</label>
                            <input type="password" id="adminPassword" name="adminPassword" required minlength="6">
                        </div>
                        <div class="form-group">
                            <label for="adminPasswordConfirm">确认密码</label>
                            <input type="password" id="adminPasswordConfirm" name="adminPasswordConfirm" required>
                        </div>
                    </div>
                </form>

                <div class="btn-group">
                    <button class="btn btn-secondary" onclick="prevStep()">上一步</button>
                    <button class="btn btn-primary" onclick="startInstall()">开始安装</button>
                </div>
            </div>

            <!-- 步骤5: 安装完成 -->
            <div class="step-content" id="step5">
                <div class="loading" id="installLoading">
                    <div class="spinner"></div>
                    <p>正在安装，请稍候...</p>
                    <div id="installProgress"></div>
                </div>

                <div id="installResult" class="hidden">
                    <div class="alert alert-success">
                        <h3>🎉 安装完成！</h3>
                        <p>恭喜您，POS企业网站已成功安装！</p>
                    </div>

                    <div style="margin: 20px 0;">
                        <h4>访问地址：</h4>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>企业官网：</strong> <a href="#" id="frontendUrl" target="_blank"></a></li>
                            <li><strong>管理后台：</strong> <a href="#" id="adminUrl" target="_blank"></a></li>
                        </ul>
                    </div>

                    <div style="margin: 20px 0;">
                        <h4>管理员账号：</h4>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            <li><strong>用户名：</strong> <span id="finalAdminUsername"></span></li>
                            <li><strong>邮箱：</strong> <span id="finalAdminEmail"></span></li>
                        </ul>
                    </div>

                    <div class="btn-group">
                        <button class="btn btn-secondary" onclick="window.open(document.getElementById('frontendUrl').href, '_blank')">访问网站</button>
                        <button class="btn btn-primary" onclick="window.open(document.getElementById('adminUrl').href, '_blank')">进入管理后台</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 检查是否已安装
        fetch('../install.lock')
            .then(response => {
                if (response.ok) {
                    // 已安装，显示提示
                    document.body.innerHTML = `
                        <div class="install-container">
                            <div class="install-header">
                                <h1>POS企业网站</h1>
                                <p>系统已安装完成</p>
                            </div>
                            <div class="install-content" style="text-align: center; padding: 60px 40px;">
                                <div class="alert alert-success">
                                    <h3>🎉 系统已安装</h3>
                                    <p>POS企业网站已成功安装并运行中！</p>
                                </div>
                                <div style="margin: 30px 0;">
                                    <p><strong>如需重新安装，请删除根目录下的 install.lock 文件</strong></p>
                                </div>
                                <div class="btn-group" style="justify-content: center; gap: 20px;">
                                    <button class="btn btn-secondary" onclick="window.open('/', '_blank')">访问网站</button>
                                    <button class="btn btn-primary" onclick="window.open('/admin', '_blank')">管理后台</button>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    // 未安装，加载安装脚本
                    const script = document.createElement('script');
                    script.src = 'install.js';
                    document.head.appendChild(script);
                }
            })
            .catch(() => {
                // 文件不存在，加载安装脚本
                const script = document.createElement('script');
                script.src = 'install.js';
                document.head.appendChild(script);
            });
    </script>
</body>
</html>
