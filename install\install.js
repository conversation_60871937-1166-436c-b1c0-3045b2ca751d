// 安装向导JavaScript

let currentStep = 1;
let maxSteps = 5;
let environmentChecked = false;
let databaseTested = false;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    updateProgress();
});

// 更新进度条
function updateProgress() {
    const progress = (currentStep / maxSteps) * 100;
    document.getElementById('progressFill').style.width = progress + '%';
}

// 更新步骤指示器
function updateStepIndicator() {
    const steps = document.querySelectorAll('.step');
    steps.forEach((step, index) => {
        const stepNumber = index + 1;
        step.classList.remove('active', 'completed');

        if (stepNumber < currentStep) {
            step.classList.add('completed');
        } else if (stepNumber === currentStep) {
            step.classList.add('active');
        }
    });
}

// 显示步骤内容
function showStep(step) {
    // 隐藏所有步骤内容
    document.querySelectorAll('.step-content').forEach(content => {
        content.classList.remove('active');
    });

    // 显示当前步骤
    document.getElementById('step' + step).classList.add('active');

    currentStep = step;
    updateProgress();
    updateStepIndicator();
}

// 下一步
function nextStep() {
    if (currentStep < maxSteps) {
        // 验证当前步骤
        if (validateCurrentStep()) {
            showStep(currentStep + 1);
        }
    }
}

// 上一步
function prevStep() {
    if (currentStep > 1) {
        showStep(currentStep - 1);
    }
}

// 验证当前步骤
function validateCurrentStep() {
    switch (currentStep) {
        case 1:
            if (!environmentChecked) {
                alert('请先检查环境要求');
                return false;
            }
            break;
        case 2:
            if (!databaseTested) {
                alert('请先测试数据库连接');
                return false;
            }
            break;
        case 3:
            const siteForm = document.getElementById('siteForm');
            if (!siteForm.checkValidity()) {
                alert('请填写完整的网站配置信息');
                return false;
            }
            break;
        case 4:
            const adminForm = document.getElementById('adminForm');
            if (!adminForm.checkValidity()) {
                alert('请填写完整的管理员信息');
                return false;
            }

            const password = document.getElementById('adminPassword').value;
            const confirmPassword = document.getElementById('adminPasswordConfirm').value;

            if (password !== confirmPassword) {
                alert('两次输入的密码不一致');
                return false;
            }
            break;
    }
    return true;
}

// 检查环境
async function checkEnvironment() {
    try {
        // 模拟环境检查
        updateRequirementStatus('nodeStatus', 'checking');
        await sleep(500);

        // 检查Node.js版本
        const nodeCheck = await checkNodeVersion();
        updateRequirementStatus('nodeStatus', nodeCheck ? 'success' : 'error');

        updateRequirementStatus('npmStatus', 'checking');
        await sleep(500);

        // 检查NPM版本
        const npmCheck = await checkNpmVersion();
        updateRequirementStatus('npmStatus', npmCheck ? 'success' : 'error');

        updateRequirementStatus('writeStatus', 'checking');
        await sleep(500);

        // 检查写入权限
        const writeCheck = await checkWritePermission();
        updateRequirementStatus('writeStatus', writeCheck ? 'success' : 'error');

        updateRequirementStatus('portStatus', 'checking');
        await sleep(500);

        // 检查端口
        const portCheck = await checkPortAvailability();
        updateRequirementStatus('portStatus', portCheck ? 'success' : 'warning');

        // 所有检查完成
        if (nodeCheck && npmCheck && writeCheck) {
            environmentChecked = true;
            showAlert('success', '环境检查通过！您可以继续下一步。');
        } else {
            showAlert('error', '环境检查未通过，请解决相关问题后重试。');
        }

    } catch (error) {
        console.error('环境检查失败:', error);
        showAlert('error', '环境检查失败: ' + error.message);
    }
}

// 更新要求状态
function updateRequirementStatus(elementId, status) {
    const element = document.getElementById(elementId);
    element.className = 'requirement-status';

    switch (status) {
        case 'success':
            element.classList.add('success');
            element.textContent = '✓';
            break;
        case 'error':
            element.classList.add('error');
            element.textContent = '✗';
            break;
        case 'warning':
            element.classList.add('warning');
            element.textContent = '!';
            break;
        case 'checking':
            element.textContent = '...';
            break;
        default:
            element.textContent = '?';
    }
}

// 实际检查函数
async function checkNodeVersion() {
    try {
        const response = await fetch('api.php?action=check_environment');
        const result = await response.json();
        return result.success && result.data.node;
    } catch (error) {
        console.error('Node.js检查失败:', error);
        return false;
    }
}

async function checkNpmVersion() {
    try {
        const response = await fetch('api.php?action=check_environment');
        const result = await response.json();
        return result.success && result.data.npm;
    } catch (error) {
        console.error('NPM检查失败:', error);
        return false;
    }
}

async function checkWritePermission() {
    try {
        const response = await fetch('api.php?action=check_environment');
        const result = await response.json();
        return result.success && result.data.write_permission;
    } catch (error) {
        console.error('写入权限检查失败:', error);
        return false;
    }
}

async function checkPortAvailability() {
    try {
        const response = await fetch('api.php?action=check_environment');
        const result = await response.json();
        return result.success && result.data.port_3001;
    } catch (error) {
        console.error('端口检查失败:', error);
        return false;
    }
}

// 测试数据库连接
async function testDatabase() {
    const form = document.getElementById('dbForm');
    const formData = new FormData(form);

    const dbConfig = {
        host: formData.get('dbHost'),
        port: formData.get('dbPort'),
        database: formData.get('dbName'),
        username: formData.get('dbUser'),
        password: formData.get('dbPassword')
    };

    try {
        showAlert('warning', '正在测试数据库连接...');

        // 调用后端API测试数据库连接
        const response = await fetch('api.php?action=test_database', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(dbConfig)
        });

        const result = await response.json();

        if (result.success) {
            databaseTested = true;
            document.getElementById('dbNextBtn').disabled = false;
            showAlert('success', '数据库连接测试成功！');
        } else {
            showAlert('error', '数据库连接失败：' + result.error);
        }

    } catch (error) {
        console.error('数据库测试失败:', error);
        showAlert('error', '数据库连接测试失败: ' + error.message);
    }
}

// 开始安装
async function startInstall() {
    if (!validateCurrentStep()) {
        return;
    }

    showStep(5);
    document.getElementById('installLoading').style.display = 'block';

    try {
        // 收集所有配置信息
        const config = collectInstallConfig();

        // 执行安装步骤
        await performInstallation(config);

        // 安装完成
        document.getElementById('installLoading').style.display = 'none';
        document.getElementById('installResult').classList.remove('hidden');

        // 设置访问链接
        const siteUrl = config.site.siteUrl;
        document.getElementById('frontendUrl').href = siteUrl;
        document.getElementById('frontendUrl').textContent = siteUrl;
        document.getElementById('adminUrl').href = siteUrl + '/admin';
        document.getElementById('adminUrl').textContent = siteUrl + '/admin';

        // 显示管理员信息
        document.getElementById('finalAdminUsername').textContent = config.admin.username;
        document.getElementById('finalAdminEmail').textContent = config.admin.email;

    } catch (error) {
        console.error('安装失败:', error);
        document.getElementById('installLoading').style.display = 'none';
        showAlert('error', '安装失败: ' + error.message);
    }
}

// 收集安装配置
function collectInstallConfig() {
    const dbForm = document.getElementById('dbForm');
    const siteForm = document.getElementById('siteForm');
    const adminForm = document.getElementById('adminForm');

    return {
        database: {
            host: dbForm.dbHost.value,
            port: dbForm.dbPort.value,
            database: dbForm.dbName.value,
            username: dbForm.dbUser.value,
            password: dbForm.dbPassword.value
        },
        site: {
            siteName: siteForm.siteName.value,
            siteUrl: siteForm.siteUrl.value,
            siteDescription: siteForm.siteDescription.value,
            companyPhone: siteForm.companyPhone.value,
            companyEmail: siteForm.companyEmail.value,
            companyAddress: siteForm.companyAddress.value
        },
        admin: {
            username: adminForm.adminUsername.value,
            email: adminForm.adminEmail.value,
            password: adminForm.adminPassword.value
        }
    };
}

// 执行安装
async function performInstallation(config) {
    try {
        updateInstallProgress('正在开始安装...');

        // 调用后端API执行完整安装
        const response = await fetch('api.php?action=install', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(config)
        });

        const result = await response.json();

        if (!result.success) {
            throw new Error(result.error);
        }

        updateInstallProgress('安装完成！');
        return result.data;

    } catch (error) {
        throw new Error('安装失败: ' + error.message);
    }
}

// 更新安装进度
function updateInstallProgress(message) {
    document.getElementById('installProgress').textContent = message;
}

// 模拟安装步骤函数
async function installDependencies() {
    // 实际调用后端API安装依赖
    console.log('Installing dependencies...');
}

async function configureDatabase(dbConfig) {
    // 实际调用后端API配置数据库
    console.log('Configuring database...', dbConfig);
}

async function initializeData(config) {
    // 实际调用后端API初始化数据
    console.log('Initializing data...', config);
}

async function buildProject() {
    // 实际调用后端API构建项目
    console.log('Building project...');
}

async function startServices() {
    // 实际调用后端API启动服务
    console.log('Starting services...');
}

async function finalizeConfiguration(config) {
    // 实际调用后端API完成配置
    console.log('Finalizing configuration...', config);
}

// 工具函数
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function showAlert(type, message) {
    // 移除现有的alert
    const existingAlert = document.querySelector('.alert');
    if (existingAlert) {
        existingAlert.remove();
    }

    // 创建新的alert
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.textContent = message;

    // 插入到当前步骤内容的开头
    const currentStepContent = document.querySelector('.step-content.active');
    currentStepContent.insertBefore(alert, currentStepContent.firstChild);

    // 3秒后自动移除成功消息
    if (type === 'success') {
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 3000);
    }
}
