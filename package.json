{"name": "pos-enterprise-website", "version": "1.0.0", "description": "企业POS产品展示与宣传网站", "private": true, "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\" \"npm run dev:admin\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "dev:admin": "cd admin && npm run dev", "build": "npm run build:frontend && npm run build:backend && npm run build:admin", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "build:admin": "cd admin && npm run build", "start": "cd backend && npm run start", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install && cd ../admin && npm install"}, "devDependencies": {"concurrently": "^8.2.2"}, "workspaces": ["frontend", "backend", "admin", "shared"]}