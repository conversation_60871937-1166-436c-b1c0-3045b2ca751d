---
description: 
globs: 
alwaysApply: true
---
# 产品展示与管理规则

本规则适用于企业POS产品的展示、管理与前后端数据结构设计。

## 1. 产品信息结构
- 产品ID（唯一标识）
- 产品名称
- 产品主图、轮播图、详情图
- 产品简介、详细描述
- 产品特点（如技术参数、适用场景、优势等）
- 所属分类、标签
- 上架/下架状态
- 发布时间、更新时间
- SEO元信息（title、description、keywords）

## 2. 产品管理建议
- 支持多图上传与排序，图片需压缩优化。
- 支持产品分类、标签管理，便于前端筛选与SEO优化。
- 支持产品内容富文本编辑。
- 支持产品置顶、推荐、排序等功能。
- 产品信息应支持多语言扩展（如有需求）。

## 3. 数据结构建议
- 推荐使用TypeScript定义产品类型。
- 产品数据结构应便于API接口输出，支持分页、筛选、搜索。

## 相关文件引用
- [管理后台开发规范](mdc:admin-panel.mdc)
- [SEO优化规则](mdc:seo-guidelines.mdc)
- [项目结构说明](mdc:project-structure.mdc)

