// 公司信息类型
export interface CompanyInfo {
  id: string;
  name: string;
  logo: string;
  description: string;
  foundedYear: number;
  address: string;
  phone: string;
  email: string;
  website: string;
  businessLicense: string;
  vision: string;
  mission: string;
  values: string[];
  createdAt: Date;
  updatedAt: Date;
}

// 产品信息类型
export interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  shortDescription: string;
  features: string[];
  specifications: Record<string, any>;
  mainImage: string;
  images: string[];
  categoryId: string;
  tags: string[];
  status: 'draft' | 'published' | 'archived';
  featured: boolean;
  sortOrder: number;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 产品分类类型
export interface ProductCategory {
  id: string;
  name: string;
  slug: string;
  description: string;
  parentId?: string;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

// 文章类型
export interface Article {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  featuredImage?: string;
  categoryId: string;
  tags: string[];
  status: 'draft' | 'published' | 'archived';
  featured: boolean;
  sortOrder: number;
  authorId: string;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// 文章分类类型
export interface ArticleCategory {
  id: string;
  name: string;
  slug: string;
  description: string;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

// 用户访问记录类型
export interface UserVisit {
  id: string;
  sessionId: string;
  ipAddress: string;
  userAgent: string;
  referer?: string;
  url: string;
  pageTitle: string;
  visitTime: Date;
  stayDuration?: number;
  deviceType: 'desktop' | 'tablet' | 'mobile';
  browser: string;
  os: string;
  country?: string;
  city?: string;
  createdAt: Date;
}

// 用户咨询表单类型
export interface ContactForm {
  id: string;
  name: string;
  phone: string;
  email?: string;
  company?: string;
  message: string;
  source: string; // 来源页面
  status: 'new' | 'contacted' | 'closed';
  createdAt: Date;
  updatedAt: Date;
}

// 管理员用户类型
export interface AdminUser {
  id: string;
  username: string;
  email: string;
  password: string;
  role: 'super_admin' | 'admin' | 'editor';
  status: 'active' | 'inactive';
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// SEO设置类型
export interface SeoSettings {
  id: string;
  page: string; // 页面标识
  title: string;
  description: string;
  keywords: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  createdAt: Date;
  updatedAt: Date;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 分页类型
export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: Pagination;
}
